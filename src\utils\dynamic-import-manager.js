// Dynamic Import Manager
// Handles lazy loading of modules based on user interactions and page requirements

class DynamicImportManager {
    constructor() {
        this.loadedModules = new Map();
        this.loadingPromises = new Map();
        this.featureModules = new Map();
        this.intersectionObserver = null;
        this.initialized = false;
        
        this.init();
    }

    init() {
        if (this.initialized) return;

        console.log('🚀 Initializing Dynamic Import Manager...');

        // Setup feature module mappings
        this.setupFeatureModules();
        
        // Setup intersection observer for lazy loading
        this.setupIntersectionObserver();
        
        // Setup event listeners for user interactions
        this.setupEventListeners();
        
        this.initialized = true;
        console.log('✅ Dynamic Import Manager initialized');
    }

    setupFeatureModules() {
        // Map features to their corresponding modules
        this.featureModules.set('charts', {
            modules: [
                () => import('chart.js'),
                () => import('../features/charts/chart-manager.js')
            ],
            trigger: '[data-chart]',
            priority: 'high'
        });

        this.featureModules.set('pdf-viewer', {
            modules: [
                () => import('pdfjs-dist'),
                () => import('../features/pdf/pdf-viewer.js')
            ],
            trigger: '[data-pdf]',
            priority: 'medium'
        });

        this.featureModules.set('math-renderer', {
            modules: [
                () => import('mathjax'),
                () => import('../features/math/math-renderer.js')
            ],
            trigger: '[data-math]',
            priority: 'medium'
        });

        this.featureModules.set('3d-visualization', {
            modules: [
                () => import('three'),
                () => import('../features/3d/three-manager.js')
            ],
            trigger: '[data-3d]',
            priority: 'low'
        });

        this.featureModules.set('rich-editor', {
            modules: [
                () => import('quill'),
                () => import('../features/editor/quill-manager.js')
            ],
            trigger: '[data-editor]',
            priority: 'high'
        });

        this.featureModules.set('speech-synthesis', {
            modules: [
                () => import('../features/speech/speech-synthesis.js')
            ],
            trigger: '[data-speech]',
            priority: 'medium'
        });

        this.featureModules.set('image-analysis', {
            modules: [
                () => import('../features/ai/image-analyzer.js')
            ],
            trigger: '[data-image-analysis]',
            priority: 'medium'
        });

        this.featureModules.set('flashcards', {
            modules: [
                () => import('../pages/flashcards/flashcard-manager.js'),
                () => import('../pages/flashcards/sm2.js')
            ],
            trigger: '[data-flashcard]',
            priority: 'high'
        });
    }

    setupIntersectionObserver() {
        if (!('IntersectionObserver' in window)) {
            console.warn('IntersectionObserver not supported, falling back to immediate loading');
            return;
        }

        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const feature = element.dataset.feature;
                    
                    if (feature && this.featureModules.has(feature)) {
                        this.loadFeature(feature);
                        this.intersectionObserver.unobserve(element);
                    }
                }
            });
        }, {
            rootMargin: '50px',
            threshold: 0.1
        });
    }

    setupEventListeners() {
        // Listen for clicks on elements that might need dynamic modules
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-feature]');
            if (target) {
                const feature = target.dataset.feature;
                if (this.featureModules.has(feature)) {
                    this.loadFeature(feature);
                }
            }
        });

        // Listen for custom events that trigger module loading
        window.addEventListener('load-feature', (e) => {
            const { feature, priority = 'medium' } = e.detail;
            this.loadFeature(feature, priority);
        });

        // Listen for page visibility changes to prioritize loading
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.loadHighPriorityFeatures();
            }
        });
    }

    // Load a specific feature module
    async loadFeature(featureName, forcePriority = null) {
        if (this.loadedModules.has(featureName)) {
            console.log(`✅ Feature already loaded: ${featureName}`);
            return this.loadedModules.get(featureName);
        }

        if (this.loadingPromises.has(featureName)) {
            console.log(`⏳ Feature already loading: ${featureName}`);
            return this.loadingPromises.get(featureName);
        }

        const featureConfig = this.featureModules.get(featureName);
        if (!featureConfig) {
            console.error(`❌ Unknown feature: ${featureName}`);
            return null;
        }

        console.log(`🔄 Loading feature: ${featureName}`);

        const loadingPromise = this.loadFeatureModules(featureName, featureConfig);
        this.loadingPromises.set(featureName, loadingPromise);

        try {
            const modules = await loadingPromise;
            this.loadedModules.set(featureName, modules);
            this.loadingPromises.delete(featureName);
            
            console.log(`✅ Feature loaded successfully: ${featureName}`);
            
            // Dispatch feature loaded event
            window.dispatchEvent(new CustomEvent('feature-loaded', {
                detail: { feature: featureName, modules }
            }));
            
            return modules;

        } catch (error) {
            console.error(`❌ Failed to load feature ${featureName}:`, error);
            this.loadingPromises.delete(featureName);
            throw error;
        }
    }

    // Load modules for a specific feature
    async loadFeatureModules(featureName, featureConfig) {
        const { modules } = featureConfig;
        const loadedModules = [];

        for (const moduleLoader of modules) {
            try {
                const module = await moduleLoader();
                loadedModules.push(module);
            } catch (error) {
                console.error(`Failed to load module for feature ${featureName}:`, error);
                // Continue loading other modules even if one fails
            }
        }

        return loadedModules;
    }

    // Load high priority features immediately
    async loadHighPriorityFeatures() {
        const highPriorityFeatures = Array.from(this.featureModules.entries())
            .filter(([_, config]) => config.priority === 'high')
            .map(([name, _]) => name);

        console.log(`🚀 Loading high priority features: ${highPriorityFeatures.join(', ')}`);

        const promises = highPriorityFeatures.map(feature => 
            this.loadFeature(feature).catch(error => {
                console.error(`Failed to load high priority feature ${feature}:`, error);
            })
        );

        await Promise.allSettled(promises);
    }

    // Load features based on page content
    async loadFeaturesForPage() {
        const featuresOnPage = new Set();

        // Scan the page for feature triggers
        this.featureModules.forEach((config, featureName) => {
            const elements = document.querySelectorAll(config.trigger);
            if (elements.length > 0) {
                featuresOnPage.add(featureName);
                
                // Setup intersection observer for lazy loading
                if (this.intersectionObserver) {
                    elements.forEach(element => {
                        element.dataset.feature = featureName;
                        this.intersectionObserver.observe(element);
                    });
                }
            }
        });

        console.log(`📄 Features detected on page: ${Array.from(featuresOnPage).join(', ')}`);

        // Load high priority features immediately
        const highPriorityFeatures = Array.from(featuresOnPage).filter(feature => {
            const config = this.featureModules.get(feature);
            return config && config.priority === 'high';
        });

        if (highPriorityFeatures.length > 0) {
            await Promise.allSettled(
                highPriorityFeatures.map(feature => this.loadFeature(feature))
            );
        }
    }

    // Preload features based on user behavior
    async preloadFeatures(features) {
        console.log(`⚡ Preloading features: ${features.join(', ')}`);

        const promises = features.map(feature => 
            this.loadFeature(feature).catch(error => {
                console.error(`Failed to preload feature ${feature}:`, error);
            })
        );

        await Promise.allSettled(promises);
    }

    // Load feature on user interaction
    async loadOnInteraction(featureName, element) {
        const loadFeatureOnce = async () => {
            await this.loadFeature(featureName);
            
            // Remove event listeners after loading
            element.removeEventListener('click', loadFeatureOnce);
            element.removeEventListener('mouseenter', loadFeatureOnce);
            element.removeEventListener('focus', loadFeatureOnce);
        };

        // Load on various user interactions
        element.addEventListener('click', loadFeatureOnce, { once: true });
        element.addEventListener('mouseenter', loadFeatureOnce, { once: true });
        element.addEventListener('focus', loadFeatureOnce, { once: true });
    }

    // Check if a feature is loaded
    isFeatureLoaded(featureName) {
        return this.loadedModules.has(featureName);
    }

    // Check if a feature is currently loading
    isFeatureLoading(featureName) {
        return this.loadingPromises.has(featureName);
    }

    // Get loaded features
    getLoadedFeatures() {
        return Array.from(this.loadedModules.keys());
    }

    // Get loading statistics
    getStats() {
        return {
            totalFeatures: this.featureModules.size,
            loadedFeatures: this.loadedModules.size,
            loadingFeatures: this.loadingPromises.size,
            loadedFeaturesList: this.getLoadedFeatures()
        };
    }

    // Cleanup
    destroy() {
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
        
        this.loadedModules.clear();
        this.loadingPromises.clear();
        this.featureModules.clear();
        
        console.log('🗑️ Dynamic Import Manager destroyed');
    }
}

// Create singleton instance
const dynamicImportManager = new DynamicImportManager();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        dynamicImportManager.loadFeaturesForPage();
    });
} else {
    dynamicImportManager.loadFeaturesForPage();
}

// Make available globally
window.dynamicImportManager = dynamicImportManager;

// Export for use in other modules
export default dynamicImportManager;
export { DynamicImportManager };
