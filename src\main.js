// Main entry point for the GPAce application
// This file will be the primary entry point for Vite bundling

// Import core modules
import { firebaseManager } from './core/firebase-init.js';
import crossTabSync from './core/cross-tab-sync.js';
import injectHeader from './core/inject-header.js';
import { initializeFirestoreData } from './services/firestore-init.js';
import sideDrawer from './ui/side-drawer.js';
import { commonUtils } from './utils/common.js';
import dynamicImportManager from './utils/dynamic-import-manager.js';
import moduleConverter from './utils/module-converter.js';

// Import the modernized module loader (fallback)
import moduleLoader from '../js/module-loader.js';
import { initializeModuleSystem } from '../js/import-maps.js';

// Import core styles (these will be bundled by Vite)
import '../css/main.css';
import '../grind.css';

// Initialize application
class GPAceApp {
    constructor() {
        this.initialized = false;
        this.currentPage = this.detectCurrentPage();
        this.moduleLoader = moduleLoader;
        this.init();
    }

    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().replace('.html', '') || 'index';
        return filename;
    }

    async init() {
        console.log(`🚀 Initializing GPAce App for page: ${this.currentPage}`);

        try {
            // Load core modules first
            await this.loadCoreModules();

            // Load page-specific modules
            await this.loadPageModules();

            // Initialize page-specific functionality
            await this.initializePage();

            this.initialized = true;
            console.log('✅ GPAce App initialized successfully');

            // Dispatch initialization complete event
            window.dispatchEvent(new CustomEvent('gpace:initialized', {
                detail: { page: this.currentPage }
            }));

        } catch (error) {
            console.error('❌ Failed to initialize GPAce App:', error);
        }
    }

    async loadCoreModules() {
        console.log('🔧 Loading core modules...');

        try {
            // Initialize Firebase first
            await firebaseManager.initialize();

            // Initialize cross-tab sync
            await crossTabSync.init();

            // Initialize header injection
            await injectHeader.init();

            // Initialize side drawer
            await sideDrawer.init();

            // Initialize common utilities
            await commonUtils.init();

            // Initialize dynamic import manager
            await dynamicImportManager.init();

            // Initialize Firestore data
            await initializeFirestoreData();

            // Load additional core modules dynamically
            const additionalCoreModules = [
                () => import('./ui/theme-manager.js'),
                () => import('./services/alarm-service.js')
            ];

            await Promise.all(additionalCoreModules.map(loader => loader()));

            console.log('✅ Core modules loaded');

        } catch (error) {
            console.error('❌ Failed to load core modules:', error);
            // Fallback to legacy module loading
            await this.loadLegacyCoreModules();
        }
    }

    async loadLegacyCoreModules() {
        console.log('📜 Loading legacy core modules...');

        try {
            // Initialize module system (fallback)
            await initializeModuleSystem();

            // Load essential modules (fallback)
            await this.moduleLoader.loadEssentialModules();

            console.log('✅ Legacy core modules loaded');
        } catch (error) {
            console.error('❌ Failed to load legacy core modules:', error);
        }
    }

    async loadPageModules() {
        // Load page-specific modules based on current page
        const pageModuleMap = {
            'study-spaces': () => this.loadStudySpacesModules(),
            'workspace': () => this.loadWorkspaceModules(),
            'academic-details': () => this.loadAcademicModules(),
            'grind': () => this.loadGrindModules(),
            'daily-calendar': () => this.loadCalendarModules(),
            'extracted': () => this.loadTaskModules(),
            'flashcards': () => this.loadFlashcardModules(),
            'tasks': () => this.loadTaskModules(),
            'settings': () => this.loadSettingsModules(),
            'subject-marks': () => this.loadMarksModules(),
            'priority-list': () => this.loadPriorityModules(),
            'priority-calculator': () => this.loadPriorityModules(),
            'sleep-saboteurs': () => this.loadSleepModules(),
            'instant-test-feedback': () => this.loadTestFeedbackModules()
        };

        const loader = pageModuleMap[this.currentPage];
        if (loader) {
            await loader();
        }
    }

    async loadStudySpacesModules() {
        const modules = [
            () => import('./pages/study-spaces/timetable-analyzer.js'),
            () => import('./pages/study-spaces/study-spaces-manager.js'),
            () => import('./pages/study-spaces/study-space-analyzer.js'),
            () => import('./pages/study-spaces/schedule-manager.js'),
            () => import('./pages/study-spaces/image-analyzer.js')
        ];

        try {
            await Promise.all(modules.map(loader => loader()));
            console.log('✅ Study Spaces modules loaded');
        } catch (error) {
            console.error('❌ Failed to load Study Spaces modules:', error);
            // Fallback to legacy scripts
            await this.loadLegacyScripts([
                '/js/timetableAnalyzer.js',
                '/js/studySpacesManager.js',
                '/js/studySpaceAnalyzer.js',
                '/js/scheduleManager.js',
                '/js/imageAnalyzer.js'
            ]);
        }
    }

    async loadWorkspaceModules() {
        const modules = [
            () =>
            import ('./pages/workspace/workspace-core.js'),
            () =>
            import ('./pages/workspace/workspace-ui.js'),
            () =>
            import ('./pages/workspace/workspace-formatting.js'),
            () =>
            import ('./pages/workspace/workspace-document.js'),
            () =>
            import ('./pages/workspace/workspace-media.js'),
            () =>
            import ('./pages/workspace/workspace-tables-links.js'),
            () =>
            import ('./pages/workspace/workspace-attachments.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadAcademicModules() {
        const modules = [
            () =>
            import ('./pages/academic/academic-details.js'),
            () =>
            import ('./pages/academic/subject-management.js'),
            () =>
            import ('./pages/academic/semester-management.js'),
            () =>
            import ('./utils/ui-utilities.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadGrindModules() {
        const modules = [
            () =>
            import ('./pages/grind/ai-latex-conversion.js'),
            () =>
            import ('./pages/grind/grind-speech-synthesis.js'),
            () =>
            import ('./pages/grind/energy-hologram.js'),
            () =>
            import ('./pages/grind/ai-researcher.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadCalendarModules() {
        const modules = [
            () =>
            import ('./pages/calendar/calendar-manager.js'),
            () =>
            import ('./pages/calendar/calendar-views.js'),
            () =>
            import ('./pages/calendar/timetable-integration.js'),
            () =>
            import ('./pages/calendar/current-task-manager.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadTaskModules() {
        const modules = [
            () =>
            import ('./pages/tasks/tasks-manager.js'),
            () =>
            import ('./pages/tasks/task-filters.js'),
            () =>
            import ('./pages/tasks/task-links.js'),
            () =>
            import ('./pages/tasks/task-attachments.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadFlashcardModules() {
        const modules = [
            () =>
            import ('./pages/flashcards/flashcard-manager.js'),
            () =>
            import ('./pages/flashcards/flashcard-task-integration.js'),
            () =>
            import ('./pages/flashcards/sm2.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadSettingsModules() {
        const modules = [
            () =>
            import ('./pages/settings/api-settings.js'),
            () =>
            import ('./pages/settings/theme-settings.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadMarksModules() {
        const modules = [
            () =>
            import ('./pages/marks/subject-marks.js'),
            () =>
            import ('./pages/marks/marks-tracking.js'),
            () =>
            import ('./pages/marks/subject-marks-ui.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadPriorityModules() {
        const modules = [
            () =>
            import ('./pages/priority/priority-list-sorting.js'),
            () =>
            import ('./pages/priority/priority-list-utils.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadSleepModules() {
        const modules = [
            () =>
            import ('./pages/sleep/sleep-saboteurs-init.js'),
            () =>
            import ('./pages/sleep/sleep-schedule-manager.js'),
            () =>
            import ('./pages/sleep/sleep-time-calculator.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async loadTestFeedbackModules() {
        const modules = [
            () =>
            import ('./pages/test-feedback/test-feedback.js')
        ];
        await Promise.all(modules.map(loader => loader()));
    }

    async initializePage() {
        // Initialize page-specific functionality
        const initMap = {
            'study-spaces': () => this.initStudySpaces(),
            'workspace': () => this.initWorkspace(),
            'academic-details': () => this.initAcademic(),
            'grind': () => this.initGrind(),
            'daily-calendar': () => this.initCalendar(),
            'extracted': () => this.initTasks(),
            'flashcards': () => this.initFlashcards()
        };

        const initializer = initMap[this.currentPage];
        if (initializer) {
            await initializer();
        }
    }

    async initStudySpaces() {
        // Initialize study spaces specific functionality
        if (window.StudySpacesManager) {
            window.studySpacesManager = new window.StudySpacesManager();
        }
    }

    async initWorkspace() {
        // Initialize workspace specific functionality
        if (window.WorkspaceCore) {
            window.workspaceCore = new window.WorkspaceCore();
        }
    }

    async initAcademic() {
        // Initialize academic details specific functionality
        if (window.AcademicDetails) {
            window.academicDetails = new window.AcademicDetails();
        }
    }

    async initGrind() {
        // Initialize grind page specific functionality
        if (window.EnergyHologram) {
            window.energyHologram = new window.EnergyHologram();
        }
    }

    async initCalendar() {
        // Initialize calendar specific functionality
        if (window.CalendarManager) {
            window.calendarManager = new window.CalendarManager();
        }
    }

    async initTasks() {
        // Initialize tasks specific functionality
        if (window.TasksManager) {
            window.tasksManager = new window.TasksManager();
        }
    }

    async initFlashcards() {
        // Initialize flashcards specific functionality
        if (window.FlashcardManager) {
            window.flashcardManager = new window.FlashcardManager();
        }
    }

    // Legacy script loader for fallback
    async loadLegacyScripts(scriptPaths) {
        console.log('📜 Loading legacy scripts as fallback...');

        const loadScript = (src) => {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        };

        try {
            await Promise.all(scriptPaths.map(path => loadScript(path)));
            console.log('✅ Legacy scripts loaded successfully');
        } catch (error) {
            console.error('❌ Failed to load legacy scripts:', error);
        }
    }

    // Get application status
    getStatus() {
        return {
            initialized: this.initialized,
            currentPage: this.currentPage,
            coreModulesLoaded: !!window.crossTabSync && !!window.auth,
            pageModulesLoaded: this.initialized
        };
    }
}

// Initialize the application when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.gpaceApp = new GPAceApp();
    });
} else {
    window.gpaceApp = new GPAceApp();
}

// Export for use in other modules
export default GPAceApp;