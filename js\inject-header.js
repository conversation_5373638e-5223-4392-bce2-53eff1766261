document.addEventListener('DOMContentLoaded', () => {
    // Create audio element for alarm sound
    const alarmAudio = document.createElement('audio');
    alarmAudio.id = 'alarm-sound';
    alarmAudio.src = '/alarm-sounds/alexa-ringtone.mp3';
    alarmAudio.preload = 'auto';
    document.body.appendChild(alarmAudio);

    // Add module support
    const moduleScript = document.createElement('script');
    moduleScript.type = 'importmap';
    moduleScript.textContent = JSON.stringify({
        imports: {
            'firebase/app': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js',
            'firebase/firestore': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js',
            'firebase/auth': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js'
        }
    });
    document.head.appendChild(moduleScript);

    // Load alarm handler as a module
    const alarmHandler = document.createElement('script');
    alarmHandler.type = 'module';
    alarmHandler.src = '/js/alarm-handler.js';
    document.body.appendChild(alarmHandler);

    // Initialize centralized service worker manager
    const serviceWorkerScript = document.createElement('script');
    serviceWorkerScript.type = 'module';
    serviceWorkerScript.src = '/js/service-worker-manager.js';
    document.body.appendChild(serviceWorkerScript);
    
    // Wait for service worker manager to initialize, then sync alarms
    serviceWorkerScript.onload = () => {
        if (window.serviceWorkerManager) {
            // Load alarms from localStorage and sync with service worker
            const alarms = JSON.parse(localStorage.getItem('alarms') || '[]');
            const activeAlarms = alarms.filter(alarm => alarm.active);
            
            if (activeAlarms.length > 0) {
                window.serviceWorkerManager.syncAlarms(activeAlarms);
            }
        }
    };

    // Request notification permission if not already granted
    if ('Notification' in window && Notification.permission !== 'granted') {
        Notification.requestPermission();
    }
});