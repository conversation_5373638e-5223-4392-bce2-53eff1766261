/**
 * Firebase Authentication Module
 * Handles Firebase initialization and authentication functionality
 */

// Import Firebase modules
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { getAuth, signInWithPopup, GoogleAuthProvider, signOut } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Firebase configuration for modern SDK
const firebaseConfig = {
    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
    authDomain: "mzm-gpace.firebaseapp.com",
    projectId: "mzm-gpace",
    storageBucket: "mzm-gpace.firebasestorage.app",
    messagingSenderId: "************",
    appId: "1:************:web:3aa05a6e133e2066c45187"
};

// Legacy configuration removed - using only modern v9+ SDK

/**
 * Initialize Firebase and set up authentication
 */
function initializeFirebase() {
    // Initialize Firebase with modern SDK
    const app = initializeApp(firebaseConfig);

    // Set up auth
    const auth = getAuth(app);
    const provider = new GoogleAuthProvider();
    provider.setCustomParameters({ prompt: 'select_account' });

    // Make available globally
    window.auth = auth;
    window.signInWithGoogle = () => signInWithPopup(auth, provider);
    window.signOutUser = () => signOut(auth);
}

// Initialize Firebase when this module is imported
initializeFirebase();

export { initializeFirebase };
