// Common utilities module for Vite bundling
// This replaces the original js/common.js

import { firebaseManager } from '../core/firebase-init.js';

class CommonUtils {
    constructor() {
        this.initialized = false;
        this.dataInitialized = false;
        this.pomodoroLoaded = false;
    }

    // Function to ensure data is initialized
    async ensureDataInitialized() {
        console.log('🔄 Checking data initialization...');
        
        // Check if data is already initialized
        const dataInitialized = localStorage.getItem('dataInitialized') === 'true';
        const subjects = JSON.parse(localStorage.getItem('academicSubjects') || '[]');
        
        if (!dataInitialized || subjects.length === 0) {
            console.log('📥 Data not initialized, starting initialization...');
            
            // Wait for Firebase to be ready
            await this.waitForFirebase();
            
            if (typeof window.initializeFirestoreData === 'function') {
                await window.initializeFirestoreData();
            } else {
                // Dynamically import and initialize Firestore data
                const { initializeFirestoreData } = await import('../services/firestore-init.js');
                await initializeFirestoreData();
            }
        } else {
            console.log('✅ Data already initialized');
            
            // Still update priority tasks to ensure they're current
            if (typeof window.updatePriorityTasks === 'function') {
                await window.updatePriorityTasks();
            }
        }
        
        this.dataInitialized = true;
    }

    // Wait for Firebase to be initialized
    async waitForFirebase() {
        return new Promise((resolve) => {
            if (firebaseManager.initialized) {
                resolve();
                return;
            }
            
            window.addEventListener('firebase:initialized', () => {
                resolve();
            }, { once: true });
            
            // Fallback timeout
            setTimeout(resolve, 5000);
        });
    }

    // Load global Pomodoro timer
    async loadGlobalPomodoro() {
        if (this.pomodoroLoaded || document.getElementById('pomodoroGlobalScript')) {
            return;
        }

        try {
            // Use dynamic import instead of script injection
            await import('../services/pomodoro-global.js');
            this.pomodoroLoaded = true;
            console.log('🍅 Pomodoro timer loaded');
        } catch (error) {
            console.error('Failed to load Pomodoro timer:', error);
            
            // Fallback to script injection
            const script = document.createElement('script');
            script.id = 'pomodoroGlobalScript';
            script.src = '/js/pomodoroGlobal.js';
            document.head.appendChild(script);
        }
    }

    // Initialize common functionality
    async initialize() {
        if (this.initialized) {
            return;
        }

        console.log('🚀 Initializing common utilities...');

        try {
            // Load Pomodoro timer
            await this.loadGlobalPomodoro();
            
            // Wait for auth to be ready, then initialize data
            setTimeout(async () => {
                await this.ensureDataInitialized();
            }, 1000);

            // Setup storage listener
            this.setupStorageListener();

            this.initialized = true;
            console.log('✅ Common utilities initialized');

            // Dispatch initialization event
            window.dispatchEvent(new CustomEvent('common:initialized'));

        } catch (error) {
            console.error('❌ Failed to initialize common utilities:', error);
        }
    }

    // Setup storage listener for cross-tab synchronization
    setupStorageListener() {
        window.addEventListener('storage', async (e) => {
            if (e.key === 'academicSubjects' || e.key?.startsWith('tasks-')) {
                console.log('🔄 Storage changed, updating data...');
                await this.ensureDataInitialized();
            }
        });

        // Listen for cross-tab storage changes
        window.addEventListener('crosstab:storage-change', async (e) => {
            const { key } = e.detail;
            if (key === 'academicSubjects' || key?.startsWith('tasks-')) {
                console.log('🔄 Cross-tab storage change detected, updating data...');
                await this.ensureDataInitialized();
            }
        });
    }

    // Utility function to get current user
    getCurrentUser() {
        return firebaseManager.getCurrentUser();
    }

    // Utility function to check authentication
    isAuthenticated() {
        return firebaseManager.isAuthenticated();
    }

    // Utility function to format date
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        
        return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options })
            .format(new Date(date));
    }

    // Utility function to format time
    formatTime(date, options = {}) {
        const defaultOptions = {
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options })
            .format(new Date(date));
    }

    // Utility function to debounce function calls
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Utility function to throttle function calls
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Utility function to generate unique ID
    generateId() {
        return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }

    // Utility function to deep clone object
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    // Utility function to check if object is empty
    isEmpty(obj) {
        if (obj == null) return true;
        if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
        return Object.keys(obj).length === 0;
    }

    // Utility function to capitalize string
    capitalize(str) {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }

    // Utility function to truncate string
    truncate(str, length = 100, suffix = '...') {
        if (!str || str.length <= length) return str;
        return str.substring(0, length) + suffix;
    }

    // Get initialization status
    getStatus() {
        return {
            initialized: this.initialized,
            dataInitialized: this.dataInitialized,
            pomodoroLoaded: this.pomodoroLoaded,
            authenticated: this.isAuthenticated()
        };
    }
}

// Create singleton instance
const commonUtils = new CommonUtils();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        commonUtils.initialize();
    });
} else {
    commonUtils.initialize();
}

// Make available globally for backward compatibility
window.commonUtils = commonUtils;
window.ensureDataInitialized = () => commonUtils.ensureDataInitialized();

// Export for use in other modules
export default commonUtils;
export { CommonUtils };
