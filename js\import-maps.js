/**
 * Centralized Import Maps Configuration
 * This module provides a unified way to manage all external library imports
 * and internal module aliases across the application.
 */

/**
 * Import map configuration for external libraries and internal modules
 */
export const importMaps = {
  // Firebase modules
  'firebase/app': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js',
  'firebase/auth': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js',
  'firebase/firestore': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js',
  'firebase/functions': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js',
  'firebase/storage': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js',

  // Three.js and addons
  'three': 'https://unpkg.com/three@0.157.0/build/three.module.js',
  'three/addons/': 'https://unpkg.com/three@0.157.0/examples/jsm/',

  // Google Generative AI
  '@google/generative-ai': 'https://esm.run/@google/generative-ai',

  // Chart.js
  'chart.js': 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js',
  'chart.js/auto': 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/auto/auto.js',

  // Bootstrap (when used as modules)
  'bootstrap': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.esm.min.js',
  '@popperjs/core': 'https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/esm/index.js',

  // Quill editor
  'quill': 'https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.min.js',

  // MathJax
  'mathjax': 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js',

  // PDF.js
  'pdfjs-dist': 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js',
  'pdfjs-dist/build/pdf.worker.js': 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js',

  // Other utilities
  'docx': 'https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js',
  'html2pdf': 'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js',
  'marked': 'https://cdn.jsdelivr.net/npm/marked@11.1.1/lib/marked.esm.js',

  // Internal module aliases
  '@core/': './js/core/',
  '@utils/': './js/utils/',
  '@services/': './js/services/',
  '@ui/': './js/ui/',
  '@pages/': './js/pages/',
  '@components/': './js/components/',

  // Core modules
  '@core/firebase': './js/firebase-config.js',
  '@core/auth': './js/firebaseAuth.js',
  '@core/firestore': './js/firestore.js',
  '@core/sync': './js/cross-tab-sync.js',
  '@core/common': './js/common.js',

  // UI modules
  '@ui/drawer': './js/sideDrawer.js',
  '@ui/theme': './js/theme-manager.js',
  '@ui/notifications': './js/notification.js',

  // Service modules
  '@services/alarm': './js/alarm-service.js',
  '@services/storage': './js/storageManager.js',
  '@services/api': './js/gemini-api.js',

  // Utility modules
  '@utils/ui': './js/ui-utilities.js',
  '@utils/data': './js/data-loader.js',
  '@utils/markdown': './js/markdown-converter.js'
};

/**
 * Creates and injects import map script tag into document head
 * @param {Object} customMaps - Additional import maps to merge
 */
export function injectImportMaps(customMaps = {}) {
  // Check if import map already exists
  const existingImportMap = document.querySelector('script[type="importmap"]');
  
  if (existingImportMap) {
    console.log('Import map already exists, skipping injection');
    return;
  }

  // Merge default maps with custom maps
  const finalMaps = { ...importMaps, ...customMaps };

  // Create import map script
  const importMapScript = document.createElement('script');
  importMapScript.type = 'importmap';
  importMapScript.textContent = JSON.stringify({
    imports: finalMaps
  }, null, 2);

  // Insert at the beginning of head (before other scripts)
  document.head.insertBefore(importMapScript, document.head.firstChild);
  
  console.log('Import maps injected successfully');
}

/**
 * Loads ES module shims for browsers that don't support import maps
 */
export function loadModuleShims() {
  // Check if browser supports import maps
  if (!HTMLScriptElement.supports || !HTMLScriptElement.supports('importmap')) {
    const shimScript = document.createElement('script');
    shimScript.async = true;
    shimScript.src = 'https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js';
    document.head.appendChild(shimScript);
    console.log('ES module shims loaded for import map support');
  }
}

/**
 * Initialize import maps and module shims
 * Call this early in your application lifecycle
 */
export function initializeModuleSystem(customMaps = {}) {
  loadModuleShims();
  
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      injectImportMaps(customMaps);
    });
  } else {
    injectImportMaps(customMaps);
  }
}

/**
 * Dynamic import with fallback for older browsers
 * @param {string} specifier - Module specifier to import
 * @returns {Promise} Module import promise
 */
export async function dynamicImport(specifier) {
  try {
    return await import(specifier);
  } catch (error) {
    console.error(`Failed to import module: ${specifier}`, error);
    throw error;
  }
}

// Auto-initialize if this module is loaded directly
if (typeof window !== 'undefined') {
  initializeModuleSystem();
}
