// CDN Fallback System
// Provides fallback to CDN resources if local bundles fail

class CDNFallback {
    constructor() {
        this.fallbackMap = new Map();
        this.loadedResources = new Set();
        this.failedResources = new Set();
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        
        this.setupFallbackMap();
    }

    setupFallbackMap() {
        // Map local resources to CDN fallbacks
        this.fallbackMap.set('bootstrap', {
            css: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
            js: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js'
        });

        this.fallbackMap.set('chart.js', {
            js: 'https://cdn.jsdelivr.net/npm/chart.js'
        });

        this.fallbackMap.set('firebase', {
            app: 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js',
            auth: 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js',
            firestore: 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js',
            functions: 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js'
        });

        this.fallbackMap.set('quill', {
            css: 'https://cdn.quilljs.com/1.3.6/quill.snow.css',
            js: 'https://cdn.quilljs.com/1.3.6/quill.min.js'
        });

        this.fallbackMap.set('three', {
            js: 'https://unpkg.com/three@0.157.0/build/three.module.js'
        });

        this.fallbackMap.set('mathjax', {
            js: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js'
        });

        this.fallbackMap.set('pdfjs', {
            js: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
        });

        this.fallbackMap.set('font-awesome', {
            css: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css'
        });

        this.fallbackMap.set('bootstrap-icons', {
            css: 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css'
        });
    }

    // Load resource with fallback
    async loadWithFallback(resourceName, type = 'js', options = {}) {
        const resourceKey = `${resourceName}-${type}`;
        
        if (this.loadedResources.has(resourceKey)) {
            return true;
        }

        if (this.failedResources.has(resourceKey)) {
            return this.loadFallback(resourceName, type, options);
        }

        try {
            // Try to load local resource first
            const success = await this.loadLocal(resourceName, type, options);
            if (success) {
                this.loadedResources.add(resourceKey);
                return true;
            }
        } catch (error) {
            console.warn(`Failed to load local ${resourceName}:`, error);
        }

        // Fallback to CDN
        this.failedResources.add(resourceKey);
        return this.loadFallback(resourceName, type, options);
    }

    // Load local resource
    async loadLocal(resourceName, type, options) {
        // This would typically be handled by Vite's import system
        // For runtime fallback, we check if the resource is available
        return new Promise((resolve, reject) => {
            if (type === 'js') {
                // Check if the module/script is available
                if (window[resourceName] || document.querySelector(`script[data-resource="${resourceName}"]`)) {
                    resolve(true);
                } else {
                    reject(new Error(`Local ${resourceName} not found`));
                }
            } else if (type === 'css') {
                // Check if the CSS is loaded
                const link = document.querySelector(`link[data-resource="${resourceName}"]`);
                if (link) {
                    resolve(true);
                } else {
                    reject(new Error(`Local ${resourceName} CSS not found`));
                }
            }
        });
    }

    // Load fallback from CDN
    async loadFallback(resourceName, type, options = {}) {
        const fallback = this.fallbackMap.get(resourceName);
        if (!fallback || !fallback[type]) {
            console.error(`No fallback available for ${resourceName} (${type})`);
            return false;
        }

        const url = fallback[type];
        console.log(`📡 Loading fallback for ${resourceName} from CDN: ${url}`);

        try {
            if (type === 'js') {
                await this.loadScript(url, options);
            } else if (type === 'css') {
                await this.loadStylesheet(url, options);
            }

            console.log(`✅ Successfully loaded ${resourceName} fallback`);
            this.loadedResources.add(`${resourceName}-${type}`);
            return true;

        } catch (error) {
            console.error(`❌ Failed to load ${resourceName} fallback:`, error);
            return false;
        }
    }

    // Load script from URL
    loadScript(url, options = {}) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.async = options.async !== false;
            script.defer = options.defer || false;
            
            if (options.type) script.type = options.type;
            if (options.crossOrigin) script.crossOrigin = options.crossOrigin;
            if (options.integrity) script.integrity = options.integrity;

            script.onload = () => resolve(script);
            script.onerror = () => reject(new Error(`Failed to load script: ${url}`));

            document.head.appendChild(script);
        });
    }

    // Load stylesheet from URL
    loadStylesheet(url, options = {}) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = url;
            
            if (options.crossOrigin) link.crossOrigin = options.crossOrigin;
            if (options.integrity) link.integrity = options.integrity;

            link.onload = () => resolve(link);
            link.onerror = () => reject(new Error(`Failed to load stylesheet: ${url}`));

            document.head.appendChild(link);
        });
    }

    // Check if resource is loaded
    isLoaded(resourceName, type = 'js') {
        return this.loadedResources.has(`${resourceName}-${type}`);
    }

    // Preload critical resources
    async preloadCritical() {
        const criticalResources = [
            { name: 'bootstrap', type: 'css' },
            { name: 'bootstrap', type: 'js' },
            { name: 'firebase', type: 'js' }
        ];

        const promises = criticalResources.map(({ name, type }) => 
            this.loadWithFallback(name, type)
        );

        try {
            await Promise.all(promises);
            console.log('✅ Critical resources preloaded');
        } catch (error) {
            console.error('❌ Failed to preload some critical resources:', error);
        }
    }

    // Load resources on demand
    async loadOnDemand(resourceName, type = 'js') {
        console.log(`📦 Loading ${resourceName} on demand...`);
        return this.loadWithFallback(resourceName, type);
    }

    // Retry failed resources
    async retryFailed() {
        const failedResources = Array.from(this.failedResources);
        this.failedResources.clear();

        for (const resourceKey of failedResources) {
            const [resourceName, type] = resourceKey.split('-');
            try {
                await this.loadWithFallback(resourceName, type);
            } catch (error) {
                console.error(`Retry failed for ${resourceName}:`, error);
            }
        }
    }

    // Get loading statistics
    getStats() {
        return {
            loaded: this.loadedResources.size,
            failed: this.failedResources.size,
            loadedResources: Array.from(this.loadedResources),
            failedResources: Array.from(this.failedResources)
        };
    }
}

// Create singleton instance
const cdnFallback = new CDNFallback();

// Make available globally
window.cdnFallback = cdnFallback;

// Export for use in other modules
export default cdnFallback;
export { CDNFallback };
