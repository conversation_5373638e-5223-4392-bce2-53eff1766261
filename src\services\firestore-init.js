// Firestore initialization service for Vite bundling
// This replaces the original js/initFirestoreData.js

import { 
    collection, 
    doc, 
    getDoc, 
    getDocs, 
    setDoc, 
    updateDoc,
    query,
    where,
    orderBy,
    limit
} from 'firebase/firestore';
import { firebaseManager } from '../core/firebase-init.js';
import crossTabSync from '../core/cross-tab-sync.js';

class FirestoreInitService {
    constructor() {
        this.initialized = false;
        this.initializationPromise = null;
        this.retryAttempts = 3;
        this.retryDelay = 1000;
    }

    // Main initialization function
    async initializeFirestoreData() {
        if (this.initialized) {
            console.log('✅ Firestore data already initialized');
            return;
        }

        if (this.initializationPromise) {
            console.log('⏳ Firestore initialization already in progress...');
            return this.initializationPromise;
        }

        this.initializationPromise = this._performInitialization();
        return this.initializationPromise;
    }

    async _performInitialization() {
        console.log('🔄 Starting Firestore data initialization...');

        try {
            // Wait for Firebase to be ready
            await this.waitForFirebase();

            // Check if user is authenticated
            const user = firebaseManager.getCurrentUser();
            if (!user) {
                console.log('⚠️ User not authenticated, skipping Firestore initialization');
                return;
            }

            // Initialize data in parallel
            await Promise.all([
                this.initializeAcademicSubjects(),
                this.initializeUserPreferences(),
                this.initializeTasks(),
                this.initializeWeightages()
            ]);

            // Mark as initialized
            localStorage.setItem('dataInitialized', 'true');
            localStorage.setItem('dataInitializedTimestamp', Date.now().toString());
            
            this.initialized = true;
            console.log('✅ Firestore data initialization completed');

            // Broadcast initialization complete
            crossTabSync.broadcastAction('firestore-initialized', {
                timestamp: Date.now(),
                userId: user.uid
            });

            // Dispatch event
            window.dispatchEvent(new CustomEvent('firestore:initialized', {
                detail: { userId: user.uid }
            }));

        } catch (error) {
            console.error('❌ Firestore initialization failed:', error);
            this.initializationPromise = null;
            throw error;
        }
    }

    async waitForFirebase() {
        return new Promise((resolve) => {
            if (firebaseManager.initialized && firebaseManager.db) {
                resolve();
                return;
            }
            
            window.addEventListener('firebase:initialized', () => {
                resolve();
            }, { once: true });
            
            // Fallback timeout
            setTimeout(resolve, 5000);
        });
    }

    async initializeAcademicSubjects() {
        try {
            console.log('📚 Initializing academic subjects...');
            
            const user = firebaseManager.getCurrentUser();
            if (!user) return;

            // Check if subjects already exist locally
            const localSubjects = JSON.parse(localStorage.getItem('academicSubjects') || '[]');
            if (localSubjects.length > 0) {
                console.log('✅ Academic subjects already exist locally');
                return;
            }

            // Try to load from Firestore
            const userDocRef = doc(firebaseManager.db, 'users', user.uid);
            const userDoc = await getDoc(userDocRef);

            if (userDoc.exists() && userDoc.data().academicSubjects) {
                const subjects = userDoc.data().academicSubjects;
                localStorage.setItem('academicSubjects', JSON.stringify(subjects));
                console.log(`✅ Loaded ${subjects.length} academic subjects from Firestore`);
            } else {
                // Initialize with default subjects
                const defaultSubjects = this.getDefaultSubjects();
                localStorage.setItem('academicSubjects', JSON.stringify(defaultSubjects));
                
                // Save to Firestore
                await setDoc(userDocRef, {
                    academicSubjects: defaultSubjects,
                    lastUpdated: new Date()
                }, { merge: true });
                
                console.log('✅ Initialized with default academic subjects');
            }

        } catch (error) {
            console.error('❌ Failed to initialize academic subjects:', error);
            // Fallback to default subjects
            const defaultSubjects = this.getDefaultSubjects();
            localStorage.setItem('academicSubjects', JSON.stringify(defaultSubjects));
        }
    }

    async initializeUserPreferences() {
        try {
            console.log('⚙️ Initializing user preferences...');
            
            const user = firebaseManager.getCurrentUser();
            if (!user) return;

            const preferencesDocRef = doc(firebaseManager.db, 'userPreferences', user.uid);
            const preferencesDoc = await getDoc(preferencesDocRef);

            if (preferencesDoc.exists()) {
                const preferences = preferencesDoc.data();
                localStorage.setItem('userPreferences', JSON.stringify(preferences));
                console.log('✅ Loaded user preferences from Firestore');
            } else {
                // Initialize with default preferences
                const defaultPreferences = this.getDefaultPreferences();
                localStorage.setItem('userPreferences', JSON.stringify(defaultPreferences));
                
                // Save to Firestore
                await setDoc(preferencesDocRef, {
                    ...defaultPreferences,
                    createdAt: new Date(),
                    lastUpdated: new Date()
                });
                
                console.log('✅ Initialized with default user preferences');
            }

        } catch (error) {
            console.error('❌ Failed to initialize user preferences:', error);
            // Fallback to default preferences
            const defaultPreferences = this.getDefaultPreferences();
            localStorage.setItem('userPreferences', JSON.stringify(defaultPreferences));
        }
    }

    async initializeTasks() {
        try {
            console.log('📋 Initializing tasks...');
            
            const user = firebaseManager.getCurrentUser();
            if (!user) return;

            // Load recent tasks from Firestore
            const tasksQuery = query(
                collection(firebaseManager.db, 'users', user.uid, 'tasks'),
                orderBy('lastUpdated', 'desc'),
                limit(50)
            );

            const tasksSnapshot = await getDocs(tasksQuery);
            const tasks = {};

            tasksSnapshot.forEach((doc) => {
                const taskData = doc.data();
                const projectId = taskData.projectId || 'default';
                
                if (!tasks[projectId]) {
                    tasks[projectId] = [];
                }
                
                tasks[projectId].push({
                    id: doc.id,
                    ...taskData
                });
            });

            // Store tasks in localStorage
            Object.keys(tasks).forEach(projectId => {
                localStorage.setItem(`tasks-${projectId}`, JSON.stringify(tasks[projectId]));
            });

            console.log(`✅ Loaded tasks for ${Object.keys(tasks).length} projects`);

        } catch (error) {
            console.error('❌ Failed to initialize tasks:', error);
        }
    }

    async initializeWeightages() {
        try {
            console.log('⚖️ Initializing weightages...');
            
            const user = firebaseManager.getCurrentUser();
            if (!user) return;

            const weightagesDocRef = doc(firebaseManager.db, 'weightages', user.uid);
            const weightagesDoc = await getDoc(weightagesDocRef);

            if (weightagesDoc.exists()) {
                const weightages = weightagesDoc.data();
                localStorage.setItem('weightages', JSON.stringify(weightages));
                console.log('✅ Loaded weightages from Firestore');
            } else {
                // Initialize with default weightages
                const defaultWeightages = this.getDefaultWeightages();
                localStorage.setItem('weightages', JSON.stringify(defaultWeightages));
                
                // Save to Firestore
                await setDoc(weightagesDocRef, {
                    ...defaultWeightages,
                    createdAt: new Date(),
                    lastUpdated: new Date()
                });
                
                console.log('✅ Initialized with default weightages');
            }

        } catch (error) {
            console.error('❌ Failed to initialize weightages:', error);
            // Fallback to default weightages
            const defaultWeightages = this.getDefaultWeightages();
            localStorage.setItem('weightages', JSON.stringify(defaultWeightages));
        }
    }

    getDefaultSubjects() {
        return [
            {
                id: 'math',
                name: 'Mathematics',
                code: 'MATH101',
                credits: 3,
                color: '#3498db',
                active: true
            },
            {
                id: 'physics',
                name: 'Physics',
                code: 'PHYS101',
                credits: 3,
                color: '#e74c3c',
                active: true
            },
            {
                id: 'chemistry',
                name: 'Chemistry',
                code: 'CHEM101',
                credits: 3,
                color: '#2ecc71',
                active: true
            }
        ];
    }

    getDefaultPreferences() {
        return {
            theme: 'dark',
            language: 'en',
            notifications: {
                email: true,
                push: true,
                reminders: true
            },
            privacy: {
                shareData: false,
                analytics: true
            },
            study: {
                pomodoroLength: 25,
                shortBreak: 5,
                longBreak: 15,
                autoStartBreaks: false
            }
        };
    }

    getDefaultWeightages() {
        return {
            currentHours: 0.3,
            currentDifficulty: 0.25,
            timeWindow: 0.25,
            timeRemaining: 0.2,
            lastUpdated: new Date()
        };
    }

    // Check if initialization is needed
    needsInitialization() {
        const dataInitialized = localStorage.getItem('dataInitialized') === 'true';
        const subjects = JSON.parse(localStorage.getItem('academicSubjects') || '[]');
        const timestamp = localStorage.getItem('dataInitializedTimestamp');
        
        // Check if data is stale (older than 24 hours)
        const isStale = timestamp && (Date.now() - parseInt(timestamp)) > 24 * 60 * 60 * 1000;
        
        return !dataInitialized || subjects.length === 0 || isStale;
    }

    // Force re-initialization
    async forceReinitialize() {
        localStorage.removeItem('dataInitialized');
        localStorage.removeItem('dataInitializedTimestamp');
        this.initialized = false;
        this.initializationPromise = null;
        
        return this.initializeFirestoreData();
    }

    // Get initialization status
    getStatus() {
        return {
            initialized: this.initialized,
            inProgress: !!this.initializationPromise,
            needsInitialization: this.needsInitialization(),
            lastInitialized: localStorage.getItem('dataInitializedTimestamp')
        };
    }
}

// Create singleton instance
const firestoreInitService = new FirestoreInitService();

// Make available globally for backward compatibility
window.initializeFirestoreData = () => firestoreInitService.initializeFirestoreData();
window.firestoreInitService = firestoreInitService;

// Export for use in other modules
export default firestoreInitService;
export { FirestoreInitService, firestoreInitService };
export const initializeFirestoreData = () => firestoreInitService.initializeFirestoreData();
