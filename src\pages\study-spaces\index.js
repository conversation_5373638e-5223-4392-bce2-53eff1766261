// Study Spaces Page Entry Point
// This is the main entry point for the study-spaces.html page

// Import core modules
import '../../main.js';

// Import page-specific modules
import studySpacesManager from './study-spaces-manager.js';
import { TimetableAnalyzer } from './timetable-analyzer.js';
import { StudySpaceAnalyzer } from './study-space-analyzer.js';
import { ScheduleManager } from './schedule-manager.js';
import { ImageAnalyzer } from './image-analyzer.js';

// Import page-specific styles
import './study-spaces.css';

class StudySpacesPage {
    constructor() {
        this.initialized = false;
        this.modules = {
            studySpacesManager: null,
            timetableAnalyzer: null,
            studySpaceAnalyzer: null,
            scheduleManager: null,
            imageAnalyzer: null
        };
        
        this.init();
    }

    async init() {
        if (this.initialized) return;

        console.log('🏢 Initializing Study Spaces Page...');

        try {
            // Wait for core modules to be ready
            await this.waitForCoreModules();
            
            // Initialize page-specific modules
            await this.initializeModules();
            
            // Setup page-specific event listeners
            this.setupEventListeners();
            
            // Initialize Socket.IO for real-time features
            await this.initializeSocket();
            
            this.initialized = true;
            console.log('✅ Study Spaces Page initialized');
            
            // Dispatch page initialization event
            window.dispatchEvent(new CustomEvent('studyspaces:page:initialized'));
            
        } catch (error) {
            console.error('❌ Failed to initialize Study Spaces Page:', error);
        }
    }

    async waitForCoreModules() {
        return new Promise((resolve) => {
            if (window.firebaseManager && window.crossTabSync) {
                resolve();
                return;
            }
            
            window.addEventListener('gpace:initialized', () => {
                resolve();
            }, { once: true });
            
            // Fallback timeout
            setTimeout(resolve, 5000);
        });
    }

    async initializeModules() {
        console.log('🔧 Initializing Study Spaces modules...');

        try {
            // Initialize Study Spaces Manager
            this.modules.studySpacesManager = studySpacesManager;
            await this.modules.studySpacesManager.init();

            // Initialize Timetable Analyzer
            this.modules.timetableAnalyzer = new TimetableAnalyzer();
            await this.modules.timetableAnalyzer.init();

            // Initialize Study Space Analyzer
            this.modules.studySpaceAnalyzer = new StudySpaceAnalyzer();
            await this.modules.studySpaceAnalyzer.init();

            // Initialize Schedule Manager
            this.modules.scheduleManager = new ScheduleManager();
            await this.modules.scheduleManager.init();

            // Initialize Image Analyzer
            this.modules.imageAnalyzer = new ImageAnalyzer();
            await this.modules.imageAnalyzer.init();

            console.log('✅ Study Spaces modules initialized');

        } catch (error) {
            console.error('❌ Failed to initialize Study Spaces modules:', error);
            throw error;
        }
    }

    setupEventListeners() {
        // Page-specific event listeners
        document.addEventListener('DOMContentLoaded', () => {
            this.setupUI();
        });

        // Handle file uploads
        const fileInput = document.getElementById('imageUpload');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileUpload(e);
            });
        }

        // Handle location selection
        const locationSelect = document.getElementById('locationSelect');
        if (locationSelect) {
            locationSelect.addEventListener('change', (e) => {
                this.handleLocationChange(e);
            });
        }

        // Handle analysis button
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.analyzeStudySpace();
            });
        }

        // Handle save button
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveStudySpace();
            });
        }

        // Handle timetable upload
        const timetableUpload = document.getElementById('timetableUpload');
        if (timetableUpload) {
            timetableUpload.addEventListener('change', (e) => {
                this.handleTimetableUpload(e);
            });
        }
    }

    async initializeSocket() {
        try {
            if (typeof io !== 'undefined') {
                const socket = io();
                
                socket.on('connect', () => {
                    console.log('🔌 Study Spaces Socket.IO connected');
                });
                
                socket.on('timetable-analysis-complete', (data) => {
                    this.handleTimetableAnalysisComplete(data);
                });
                
                socket.on('study-space-recommendation', (data) => {
                    this.handleStudySpaceRecommendation(data);
                });
                
                this.socket = socket;
            }
        } catch (error) {
            console.error('Failed to initialize Socket.IO:', error);
        }
    }

    setupUI() {
        // Initialize UI components
        this.initializeImagePreview();
        this.initializeLocationSelector();
        this.initializeAnalysisResults();
        this.initializeSavedSpaces();
    }

    initializeImagePreview() {
        const previewContainer = document.getElementById('imagePreview');
        if (previewContainer) {
            previewContainer.innerHTML = `
                <div class="image-preview-placeholder">
                    <i class="bi bi-image"></i>
                    <p>Upload images to preview them here</p>
                </div>
            `;
        }
    }

    initializeLocationSelector() {
        const locationSelect = document.getElementById('locationSelect');
        if (locationSelect && locationSelect.options.length <= 1) {
            // Add default location options
            const locations = [
                { value: '', text: 'Select a location...' },
                { value: 'library', text: 'Library' },
                { value: 'study-hall', text: 'Study Hall' },
                { value: 'cafe', text: 'Cafe' },
                { value: 'dorm-room', text: 'Dorm Room' },
                { value: 'outdoor', text: 'Outdoor Space' },
                { value: 'other', text: 'Other' }
            ];

            locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location.value;
                option.textContent = location.text;
                locationSelect.appendChild(option);
            });
        }
    }

    initializeAnalysisResults() {
        const resultsContainer = document.getElementById('analysisResults');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }

    initializeSavedSpaces() {
        const savedSpacesList = document.getElementById('savedSpacesList');
        if (savedSpacesList) {
            this.loadSavedSpaces();
        }
    }

    async handleFileUpload(event) {
        if (this.modules.studySpacesManager) {
            await this.modules.studySpacesManager.handleFileUpload(event);
        }
    }

    handleLocationChange(event) {
        if (this.modules.studySpacesManager) {
            this.modules.studySpacesManager.handleLocationChange(event);
        }
    }

    async analyzeStudySpace() {
        if (this.modules.studySpacesManager) {
            await this.modules.studySpacesManager.analyzeStudySpace();
        }
    }

    async saveStudySpace() {
        if (this.modules.studySpacesManager) {
            await this.modules.studySpacesManager.saveStudySpace();
        }
    }

    async handleTimetableUpload(event) {
        if (this.modules.timetableAnalyzer) {
            await this.modules.timetableAnalyzer.analyzeTimetable(event.target.files[0]);
        }
    }

    handleTimetableAnalysisComplete(data) {
        console.log('📅 Timetable analysis complete:', data);
        
        if (this.modules.scheduleManager) {
            this.modules.scheduleManager.updateSchedule(data);
        }
    }

    handleStudySpaceRecommendation(data) {
        console.log('💡 Study space recommendation:', data);
        
        // Display recommendation in UI
        this.displayRecommendation(data);
    }

    displayRecommendation(recommendation) {
        const recommendationContainer = document.getElementById('recommendations');
        if (recommendationContainer) {
            recommendationContainer.innerHTML = `
                <div class="recommendation-card">
                    <h4>Recommended Study Space</h4>
                    <p><strong>Location:</strong> ${recommendation.location}</p>
                    <p><strong>Best Time:</strong> ${recommendation.bestTime}</p>
                    <p><strong>Reason:</strong> ${recommendation.reason}</p>
                    <div class="recommendation-score">
                        <span>Suitability: ${recommendation.score}/100</span>
                    </div>
                </div>
            `;
        }
    }

    async loadSavedSpaces() {
        try {
            const savedSpaces = JSON.parse(localStorage.getItem('studySpaces') || '[]');
            const savedSpacesList = document.getElementById('savedSpacesList');
            
            if (savedSpacesList) {
                savedSpacesList.innerHTML = savedSpaces.map(space => `
                    <div class="saved-space-item" data-space-id="${space.id}">
                        <h5>${space.location}</h5>
                        <p>Suitability: ${space.analysis?.suitability || 'N/A'}/100</p>
                        <small>${new Date(space.timestamp).toLocaleDateString()}</small>
                        <button class="btn btn-sm btn-primary load-space" data-space-id="${space.id}">
                            Load
                        </button>
                    </div>
                `).join('');

                // Add event listeners for load buttons
                savedSpacesList.querySelectorAll('.load-space').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const spaceId = e.target.dataset.spaceId;
                        this.loadSavedSpace(spaceId);
                    });
                });
            }
        } catch (error) {
            console.error('Failed to load saved spaces:', error);
        }
    }

    loadSavedSpace(spaceId) {
        try {
            const savedSpaces = JSON.parse(localStorage.getItem('studySpaces') || '[]');
            const space = savedSpaces.find(s => s.id === spaceId);
            
            if (space && this.modules.studySpacesManager) {
                this.modules.studySpacesManager.loadSpace(space);
            }
        } catch (error) {
            console.error('Failed to load saved space:', error);
        }
    }

    // Get page status
    getStatus() {
        return {
            initialized: this.initialized,
            modules: Object.keys(this.modules).reduce((status, key) => {
                status[key] = !!this.modules[key];
                return status;
            }, {}),
            socketConnected: !!this.socket?.connected
        };
    }

    // Cleanup
    destroy() {
        if (this.socket) {
            this.socket.disconnect();
        }
        
        Object.values(this.modules).forEach(module => {
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }
        });
        
        console.log('🗑️ Study Spaces Page destroyed');
    }
}

// Initialize the page when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.studySpacesPage = new StudySpacesPage();
    });
} else {
    window.studySpacesPage = new StudySpacesPage();
}

// Export for use in other modules
export default StudySpacesPage;
