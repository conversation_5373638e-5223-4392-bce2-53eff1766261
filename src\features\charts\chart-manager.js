// Chart Manager - Dynamically loaded chart functionality
// This module is loaded only when charts are needed

import { Chart, registerables } from 'chart.js';

class ChartManager {
    constructor() {
        this.charts = new Map();
        this.initialized = false;
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        display: false
                    }
                },
                y: {
                    display: true,
                    beginAtZero: true
                }
            }
        };
        
        this.init();
    }

    async init() {
        if (this.initialized) return;

        console.log('📊 Initializing Chart Manager...');

        try {
            // Register Chart.js components
            Chart.register(...registerables);
            
            // Setup default configurations
            this.setupDefaultConfigs();
            
            // Initialize existing chart elements
            await this.initializeExistingCharts();
            
            this.initialized = true;
            console.log('✅ Chart Manager initialized');
            
            // Dispatch initialization event
            window.dispatchEvent(new CustomEvent('chartmanager:initialized'));
            
        } catch (error) {
            console.error('❌ Failed to initialize Chart Manager:', error);
        }
    }

    setupDefaultConfigs() {
        // Set global Chart.js defaults
        Chart.defaults.font.family = 'Inter, system-ui, sans-serif';
        Chart.defaults.color = '#6b7280';
        Chart.defaults.borderColor = '#e5e7eb';
        Chart.defaults.backgroundColor = 'rgba(59, 130, 246, 0.1)';
    }

    async initializeExistingCharts() {
        // Find all chart elements on the page
        const chartElements = document.querySelectorAll('[data-chart]');
        
        for (const element of chartElements) {
            try {
                await this.createChartFromElement(element);
            } catch (error) {
                console.error('Failed to initialize chart:', error);
            }
        }
    }

    async createChartFromElement(element) {
        const chartType = element.dataset.chart;
        const chartData = element.dataset.chartData;
        const chartOptions = element.dataset.chartOptions;

        if (!chartType) {
            console.error('Chart type not specified');
            return null;
        }

        let data, options;

        try {
            data = chartData ? JSON.parse(chartData) : this.getDefaultData(chartType);
            options = chartOptions ? JSON.parse(chartOptions) : this.getDefaultOptions(chartType);
        } catch (error) {
            console.error('Failed to parse chart configuration:', error);
            return null;
        }

        return this.createChart(element, chartType, data, options);
    }

    createChart(canvas, type, data, options = {}) {
        if (!canvas || !canvas.getContext) {
            console.error('Invalid canvas element provided');
            return null;
        }

        const chartId = canvas.id || `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        // Destroy existing chart if it exists
        if (this.charts.has(chartId)) {
            this.destroyChart(chartId);
        }

        try {
            const mergedOptions = this.mergeOptions(this.defaultOptions, options);
            
            const chart = new Chart(canvas, {
                type,
                data,
                options: mergedOptions
            });

            this.charts.set(chartId, chart);
            canvas.id = chartId;

            console.log(`✅ Chart created: ${chartId} (${type})`);
            
            // Dispatch chart created event
            window.dispatchEvent(new CustomEvent('chart:created', {
                detail: { chartId, type, chart }
            }));

            return chart;

        } catch (error) {
            console.error('Failed to create chart:', error);
            return null;
        }
    }

    updateChart(chartId, newData, newOptions = null) {
        const chart = this.charts.get(chartId);
        if (!chart) {
            console.error(`Chart not found: ${chartId}`);
            return false;
        }

        try {
            // Update data
            if (newData) {
                chart.data = newData;
            }

            // Update options
            if (newOptions) {
                chart.options = this.mergeOptions(chart.options, newOptions);
            }

            chart.update();
            
            console.log(`✅ Chart updated: ${chartId}`);
            
            // Dispatch chart updated event
            window.dispatchEvent(new CustomEvent('chart:updated', {
                detail: { chartId, chart }
            }));

            return true;

        } catch (error) {
            console.error(`Failed to update chart ${chartId}:`, error);
            return false;
        }
    }

    destroyChart(chartId) {
        const chart = this.charts.get(chartId);
        if (chart) {
            chart.destroy();
            this.charts.delete(chartId);
            
            console.log(`🗑️ Chart destroyed: ${chartId}`);
            
            // Dispatch chart destroyed event
            window.dispatchEvent(new CustomEvent('chart:destroyed', {
                detail: { chartId }
            }));
        }
    }

    getChart(chartId) {
        return this.charts.get(chartId);
    }

    getAllCharts() {
        return Array.from(this.charts.values());
    }

    getDefaultData(type) {
        const defaultData = {
            line: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Dataset 1',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            bar: {
                labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
                datasets: [{
                    label: 'Dataset 1',
                    data: [12, 19, 3, 5, 2, 3],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                        'rgba(249, 115, 22, 0.8)'
                    ]
                }]
            },
            pie: {
                labels: ['Red', 'Blue', 'Yellow'],
                datasets: [{
                    data: [300, 50, 100],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(251, 191, 36, 0.8)'
                    ]
                }]
            },
            doughnut: {
                labels: ['Red', 'Blue', 'Yellow'],
                datasets: [{
                    data: [300, 50, 100],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(251, 191, 36, 0.8)'
                    ]
                }]
            }
        };

        return defaultData[type] || defaultData.line;
    }

    getDefaultOptions(type) {
        const typeSpecificOptions = {
            line: {
                elements: {
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            },
            bar: {
                plugins: {
                    legend: {
                        display: false
                    }
                }
            },
            pie: {
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            },
            doughnut: {
                plugins: {
                    legend: {
                        position: 'right'
                    }
                },
                cutout: '60%'
            }
        };

        return typeSpecificOptions[type] || {};
    }

    mergeOptions(defaultOptions, customOptions) {
        return this.deepMerge(defaultOptions, customOptions);
    }

    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }

    // Utility method to create charts from data
    async createChartsFromData(chartsData) {
        const results = [];
        
        for (const chartData of chartsData) {
            const { canvasId, type, data, options } = chartData;
            const canvas = document.getElementById(canvasId);
            
            if (canvas) {
                const chart = this.createChart(canvas, type, data, options);
                results.push({ canvasId, chart, success: !!chart });
            } else {
                results.push({ canvasId, chart: null, success: false, error: 'Canvas not found' });
            }
        }
        
        return results;
    }

    // Get statistics
    getStats() {
        return {
            totalCharts: this.charts.size,
            chartTypes: Array.from(this.charts.values()).reduce((types, chart) => {
                types[chart.config.type] = (types[chart.config.type] || 0) + 1;
                return types;
            }, {}),
            initialized: this.initialized
        };
    }

    // Cleanup
    destroy() {
        // Destroy all charts
        this.charts.forEach((chart, chartId) => {
            this.destroyChart(chartId);
        });
        
        this.charts.clear();
        this.initialized = false;
        
        console.log('🗑️ Chart Manager destroyed');
    }
}

// Create singleton instance
const chartManager = new ChartManager();

// Make available globally for backward compatibility
window.chartManager = chartManager;
window.ChartManager = ChartManager;

// Export for use in other modules
export default chartManager;
export { ChartManager };
