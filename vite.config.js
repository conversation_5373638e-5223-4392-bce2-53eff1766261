import { defineConfig } from 'vite';
import { resolve } from 'path';
import legacy from '@vitejs/plugin-legacy';
import { createHtmlPlugin } from 'vite-plugin-html';

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';
  const isAnalyze = mode === 'analyze';

  return {
    // Base configuration
    base: './',
    
    // Build configuration with tree shaking optimization
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: !isProduction,
      minify: isProduction ? 'terser' : false,

      // Tree shaking and optimization settings
      target: 'es2020',
      cssCodeSplit: true,
      reportCompressedSize: true,
      chunkSizeWarningLimit: 1000,

      // Enable tree shaking for all modules
      rollupOptions: {
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          tryCatchDeoptimization: false
        }
      },
      
      // Rollup options for advanced bundling
      rollupOptions: {
        input: {
          // Main pages
          index: resolve(__dirname, 'index.html'),
          landing: resolve(__dirname, 'landing.html'),
          grind: resolve(__dirname, 'grind.html'),
          'study-spaces': resolve(__dirname, 'study-spaces.html'),
          workspace: resolve(__dirname, 'workspace.html'),
          'academic-details': resolve(__dirname, 'academic-details.html'),
          'daily-calendar': resolve(__dirname, 'daily-calendar.html'),
          extracted: resolve(__dirname, 'extracted.html'),
          flashcards: resolve(__dirname, 'flashcards.html'),
          tasks: resolve(__dirname, 'tasks.html'),
          settings: resolve(__dirname, 'settings.html'),
          'subject-marks': resolve(__dirname, 'subject-marks.html'),
          'priority-list': resolve(__dirname, 'priority-list.html'),
          'priority-calculator': resolve(__dirname, 'priority-calculator.html'),
          'sleep-saboteurs': resolve(__dirname, 'sleep-saboteurs.html'),
          'instant-test-feedback': resolve(__dirname, 'instant-test-feedback.html'),
          
          // Subdirectory pages
          'relaxed-mode': resolve(__dirname, 'relaxed-mode/index.html'),

          // Page-specific JavaScript entry points
          'study-spaces-page': resolve(__dirname, 'src/pages/study-spaces/index.js'),
          'workspace-page': resolve(__dirname, 'src/pages/workspace/index.js'),
          'academic-page': resolve(__dirname, 'src/pages/academic/index.js'),
          'grind-page': resolve(__dirname, 'src/pages/grind/index.js'),
          'calendar-page': resolve(__dirname, 'src/pages/calendar/index.js'),
          'flashcards-page': resolve(__dirname, 'src/pages/flashcards/index.js'),

          // Core modules entry point
          'core-modules': resolve(__dirname, 'src/main.js'),
        },
        
        output: {
          // Advanced code splitting configuration
          manualChunks: (id) => {
            // Vendor chunk splitting based on package size and usage
            if (id.includes('node_modules')) {
              // Large vendor libraries get their own chunks
              if (id.includes('firebase')) return 'vendor-firebase';
              if (id.includes('chart.js')) return 'vendor-charts';
              if (id.includes('bootstrap')) return 'vendor-ui';
              if (id.includes('quill')) return 'vendor-editor';
              if (id.includes('three')) return 'vendor-3d';
              if (id.includes('mathjax')) return 'vendor-math';
              if (id.includes('pdfjs-dist')) return 'vendor-pdf';

              // Small utilities can be grouped
              if (id.includes('lodash') || id.includes('date-fns') || id.includes('uuid')) {
                return 'vendor-utils';
              }

              // Everything else goes to vendor-misc
              return 'vendor-misc';
            }

            // Page-specific chunk splitting
            if (id.includes('/src/pages/study-spaces/')) return 'page-study-spaces';
            if (id.includes('/src/pages/workspace/')) return 'page-workspace';
            if (id.includes('/src/pages/academic/')) return 'page-academic';
            if (id.includes('/src/pages/grind/')) return 'page-grind';
            if (id.includes('/src/pages/flashcards/')) return 'page-flashcards';
            if (id.includes('/src/pages/calendar/')) return 'page-calendar';

            // Feature-specific chunk splitting
            if (id.includes('/src/features/charts/')) return 'feature-charts';
            if (id.includes('/src/features/pdf/')) return 'feature-pdf';
            if (id.includes('/src/features/3d/')) return 'feature-3d';
            if (id.includes('/src/features/editor/')) return 'feature-editor';
            if (id.includes('/src/features/ai/')) return 'feature-ai';

            // Core modules chunk
            if (id.includes('/src/core/') || id.includes('/src/ui/') || id.includes('/src/services/')) {
              return 'core-modules';
            }

            // Utilities chunk
            if (id.includes('/src/utils/')) return 'utils';

            // Legacy JS files (fallback)
            if (id.includes('/js/') && !id.includes('node_modules')) {
              if (id.includes('timetableAnalyzer') || id.includes('studySpaces')) return 'legacy-study-spaces';
              if (id.includes('workspace') || id.includes('task')) return 'legacy-workspace';
              if (id.includes('grind') || id.includes('ai-')) return 'legacy-grind';
              return 'legacy-common';
            }
          },

          // Static manual chunks for specific optimizations
          manualChunks_static: {
            // Vendor chunks - using local packages
            'vendor-firebase': [
              'firebase/app',
              'firebase/auth',
              'firebase/firestore',
              'firebase/functions'
            ],

            'vendor-ui': [
              'bootstrap',
              'bootstrap/dist/css/bootstrap.min.css'
            ],

            'vendor-charts': [
              'chart.js'
            ],

            'vendor-editor': [
              'quill'
            ],

            'vendor-3d': [
              'three'
            ],

            'vendor-math': [
              'mathjax'
            ],

            'vendor-pdf': [
              'pdfjs-dist'
            ],
            
            // Core application chunks
            'core': [
              './js/cross-tab-sync.js',
              './js/inject-header.js',
              './js/firebaseAuth.js',
              './js/firebaseConfig.js'
            ],
            
            // Common UI chunks
            'ui-common': [
              './js/sideDrawer.js',
              './js/theme-manager.js',
              './js/alarm-service.js'
            ],
            
            // Data management chunks
            'data-common': [
              './js/firestore.js',
              './js/initFirestoreData.js',
              './js/common.js',
              './js/data-sync-manager.js'
            ],
            
            // Page-specific chunks (legacy)
            'study-spaces': [
              './js/timetableAnalyzer.js',
              './js/studySpacesManager.js',
              './js/studySpaceAnalyzer.js',
              './js/scheduleManager.js',
              './js/imageAnalyzer.js'
            ],
            
            'workspace': [
              './js/workspace-core.js',
              './js/workspace-ui.js',
              './js/workspace-formatting.js',
              './js/workspace-document.js',
              './js/workspace-media.js',
              './js/workspace-tables-links.js',
              './js/workspace-attachments.js'
            ],
            
            'academic': [
              './js/academic-details.js',
              './js/subject-management.js',
              './js/semester-management.js',
              './js/ui-utilities.js'
            ],
            
            'grind': [
              './js/ai-latex-conversion.js',
              './js/grind-speech-synthesis.js',
              './js/energyHologram.js',
              './js/ai-researcher.js'
            ],

            // New modular chunks
            'page-study-spaces-new': [
              './src/pages/study-spaces/study-spaces-manager.js',
              './src/pages/study-spaces/timetable-analyzer.js',
              './src/pages/study-spaces/study-space-analyzer.js',
              './src/pages/study-spaces/schedule-manager.js',
              './src/pages/study-spaces/image-analyzer.js'
            ],

            'page-workspace-new': [
              './src/pages/workspace/task-manager.js',
              './src/pages/workspace/project-manager.js',
              './src/pages/workspace/flashcard-integration.js',
              './src/pages/workspace/workspace-analyzer.js'
            ],

            // Core modules chunk
            'core-modules-new': [
              './src/core/firebase-init.js',
              './src/core/cross-tab-sync.js',
              './src/core/inject-header.js',
              './src/ui/side-drawer.js',
              './src/utils/common.js'
            ],

            // Feature modules chunk
            'features-new': [
              './src/features/charts/chart-manager.js',
              './src/utils/dynamic-import-manager.js',
              './src/utils/module-converter.js'
            ]
          },
          
          // Advanced code splitting configuration
          experimentalMinChunkSize: 20000, // 20KB minimum chunk size

          // Asset naming with intelligent chunking
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId;

            // Page-specific chunks
            if (facadeModuleId && facadeModuleId.includes('/pages/')) {
              const pageName = facadeModuleId.split('/pages/')[1].split('/')[0];
              return isProduction ? `js/pages/${pageName}-[hash].js` : `js/pages/${pageName}.js`;
            }

            // Feature chunks
            if (facadeModuleId && facadeModuleId.includes('/features/')) {
              const featureName = facadeModuleId.split('/features/')[1].split('/')[0];
              return isProduction ? `js/features/${featureName}-[hash].js` : `js/features/${featureName}.js`;
            }

            // Vendor chunks
            if (chunkInfo.name && chunkInfo.name.startsWith('vendor-')) {
              return isProduction ? `js/vendor/[name]-[hash].js` : `js/vendor/[name].js`;
            }

            // Default naming
            return isProduction ? 'js/[name]-[hash].js' : 'js/[name].js';
          },

          entryFileNames: isProduction ? 'js/[name]-[hash].js' : 'js/[name].js',
          assetFileNames: isProduction ? 'assets/[name]-[hash].[ext]' : 'assets/[name].[ext]'
        }
      },
      
      // Terser options for production with tree shaking optimization
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug'],
          passes: 2,
          unsafe: true,
          unsafe_comps: true,
          unsafe_math: true,
          unsafe_methods: true,
          unsafe_proto: true,
          unsafe_regexp: true,
          unsafe_undefined: true
        },
        mangle: {
          safari10: true,
          properties: {
            regex: /^_/
          }
        },
        format: {
          comments: false
        }
      } : undefined,

      // Tree shaking configuration
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
        unknownGlobalSideEffects: false
      },
      
      // Bundle size analysis
      ...(isAnalyze && {
        rollupOptions: {
          ...this.rollupOptions,
          plugins: [
            // Add bundle analyzer plugin here if needed
          ]
        }
      })
    },
    
    // Development server configuration
    server: {
      port: 3000,
      open: true,
      cors: true,
      proxy: {
        '/socket.io': {
          target: 'http://localhost:3001',
          ws: true
        }
      }
    },
    
    // Preview server configuration
    preview: {
      port: 4173,
      open: true
    },
    
    // Plugins
    plugins: [
      // Legacy browser support
      legacy({
        targets: ['defaults', 'not IE 11']
      }),
      
      // HTML processing
      createHtmlPlugin({
        minify: isProduction,
        pages: [
          {
            entry: 'src/main.js',
            filename: 'index.html',
            template: 'index.html'
          }
        ]
      })
    ],
    
    // Optimization
    optimizeDeps: {
      include: [
        // Pre-bundle these dependencies
        'firebase/app',
        'firebase/auth',
        'firebase/firestore',
        'firebase/functions'
      ],
      exclude: [
        // Don't pre-bundle these
      ]
    },
    
    // CSS configuration
    css: {
      devSourcemap: !isProduction,
      preprocessorOptions: {
        scss: {
          additionalData: `@import "./css/variables.scss";`
        }
      }
    },
    
    // Define global constants
    define: {
      __DEV__: !isProduction,
      __PROD__: isProduction
    }
  };
});
