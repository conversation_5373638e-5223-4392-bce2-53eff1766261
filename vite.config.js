import { defineConfig } from 'vite';
import { resolve } from 'path';
import legacy from '@vitejs/plugin-legacy';
import { createHtmlPlugin } from 'vite-plugin-html';

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';
  const isAnalyze = mode === 'analyze';

  return {
    // Base configuration
    base: './',
    
    // Build configuration
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: !isProduction,
      minify: isProduction ? 'terser' : false,
      
      // Rollup options for advanced bundling
      rollupOptions: {
        input: {
          // Main pages
          index: resolve(__dirname, 'index.html'),
          landing: resolve(__dirname, 'landing.html'),
          grind: resolve(__dirname, 'grind.html'),
          'study-spaces': resolve(__dirname, 'study-spaces.html'),
          workspace: resolve(__dirname, 'workspace.html'),
          'academic-details': resolve(__dirname, 'academic-details.html'),
          'daily-calendar': resolve(__dirname, 'daily-calendar.html'),
          extracted: resolve(__dirname, 'extracted.html'),
          flashcards: resolve(__dirname, 'flashcards.html'),
          tasks: resolve(__dirname, 'tasks.html'),
          settings: resolve(__dirname, 'settings.html'),
          'subject-marks': resolve(__dirname, 'subject-marks.html'),
          'priority-list': resolve(__dirname, 'priority-list.html'),
          'priority-calculator': resolve(__dirname, 'priority-calculator.html'),
          'sleep-saboteurs': resolve(__dirname, 'sleep-saboteurs.html'),
          'instant-test-feedback': resolve(__dirname, 'instant-test-feedback.html'),
          
          // Subdirectory pages
          'relaxed-mode': resolve(__dirname, 'relaxed-mode/index.html'),
        },
        
        output: {
          // Code splitting configuration
          manualChunks: {
            // Vendor chunks - using local packages
            'vendor-firebase': [
              'firebase/app',
              'firebase/auth',
              'firebase/firestore',
              'firebase/functions'
            ],

            'vendor-ui': [
              'bootstrap',
              'bootstrap/dist/css/bootstrap.min.css'
            ],

            'vendor-charts': [
              'chart.js'
            ],

            'vendor-editor': [
              'quill'
            ],

            'vendor-3d': [
              'three'
            ],

            'vendor-math': [
              'mathjax'
            ],

            'vendor-pdf': [
              'pdfjs-dist'
            ],
            
            // Core application chunks
            'core': [
              './js/cross-tab-sync.js',
              './js/inject-header.js',
              './js/firebaseAuth.js',
              './js/firebaseConfig.js'
            ],
            
            // Common UI chunks
            'ui-common': [
              './js/sideDrawer.js',
              './js/theme-manager.js',
              './js/alarm-service.js'
            ],
            
            // Data management chunks
            'data-common': [
              './js/firestore.js',
              './js/initFirestoreData.js',
              './js/common.js',
              './js/data-sync-manager.js'
            ],
            
            // Page-specific chunks
            'study-spaces': [
              './js/timetableAnalyzer.js',
              './js/studySpacesManager.js',
              './js/studySpaceAnalyzer.js',
              './js/scheduleManager.js',
              './js/imageAnalyzer.js'
            ],
            
            'workspace': [
              './js/workspace-core.js',
              './js/workspace-ui.js',
              './js/workspace-formatting.js',
              './js/workspace-document.js',
              './js/workspace-media.js',
              './js/workspace-tables-links.js',
              './js/workspace-attachments.js'
            ],
            
            'academic': [
              './js/academic-details.js',
              './js/subject-management.js',
              './js/semester-management.js',
              './js/ui-utilities.js'
            ],
            
            'grind': [
              './js/ai-latex-conversion.js',
              './js/grind-speech-synthesis.js',
              './js/energyHologram.js',
              './js/ai-researcher.js'
            ]
          },
          
          // Asset naming
          chunkFileNames: isProduction ? 'js/[name]-[hash].js' : 'js/[name].js',
          entryFileNames: isProduction ? 'js/[name]-[hash].js' : 'js/[name].js',
          assetFileNames: isProduction ? 'assets/[name]-[hash].[ext]' : 'assets/[name].[ext]'
        }
      },
      
      // Terser options for production
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      } : undefined,
      
      // Bundle size analysis
      ...(isAnalyze && {
        rollupOptions: {
          ...this.rollupOptions,
          plugins: [
            // Add bundle analyzer plugin here if needed
          ]
        }
      })
    },
    
    // Development server configuration
    server: {
      port: 3000,
      open: true,
      cors: true,
      proxy: {
        '/socket.io': {
          target: 'http://localhost:3001',
          ws: true
        }
      }
    },
    
    // Preview server configuration
    preview: {
      port: 4173,
      open: true
    },
    
    // Plugins
    plugins: [
      // Legacy browser support
      legacy({
        targets: ['defaults', 'not IE 11']
      }),
      
      // HTML processing
      createHtmlPlugin({
        minify: isProduction,
        pages: [
          {
            entry: 'src/main.js',
            filename: 'index.html',
            template: 'index.html'
          }
        ]
      })
    ],
    
    // Optimization
    optimizeDeps: {
      include: [
        // Pre-bundle these dependencies
        'firebase/app',
        'firebase/auth',
        'firebase/firestore',
        'firebase/functions'
      ],
      exclude: [
        // Don't pre-bundle these
      ]
    },
    
    // CSS configuration
    css: {
      devSourcemap: !isProduction,
      preprocessorOptions: {
        scss: {
          additionalData: `@import "./css/variables.scss";`
        }
      }
    },
    
    // Define global constants
    define: {
      __DEV__: !isProduction,
      __PROD__: isProduction
    }
  };
});
