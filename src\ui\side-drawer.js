// Side drawer module for Vite bundling
// This replaces the original js/sideDrawer.js

import { firebaseManager } from '../core/firebase-init.js';
import crossTabSync from '../core/cross-tab-sync.js';

class SideDrawer {
    constructor() {
        this.isOpen = false;
        this.initialized = false;
        this.authStateListener = null;
        this.currentUser = null;
        
        this.init();
    }

    async init() {
        if (this.initialized) return;

        console.log('🎨 Initializing Side Drawer...');

        try {
            // Wait for Firebase to be ready
            await this.waitForFirebase();
            
            // Create drawer HTML structure
            this.createDrawerStructure();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Setup auth state listener
            this.setupAuthStateListener();
            
            // Load user data
            await this.loadUserData();
            
            this.initialized = true;
            console.log('✅ Side Drawer initialized');
            
            // Dispatch initialization event
            window.dispatchEvent(new CustomEvent('sidedrawer:initialized'));
            
        } catch (error) {
            console.error('❌ Failed to initialize Side Drawer:', error);
        }
    }

    async waitForFirebase() {
        return new Promise((resolve) => {
            if (firebaseManager.initialized) {
                resolve();
                return;
            }
            
            window.addEventListener('firebase:initialized', () => {
                resolve();
            }, { once: true });
            
            // Fallback timeout
            setTimeout(resolve, 5000);
        });
    }

    createDrawerStructure() {
        // Check if drawer already exists
        if (document.getElementById('sideDrawer')) {
            return;
        }

        const drawerHTML = `
            <div id="sideDrawer" class="side-drawer">
                <div class="drawer-overlay" id="drawerOverlay"></div>
                <div class="drawer-content">
                    <div class="drawer-header">
                        <div class="user-info" id="userInfo">
                            <div class="user-avatar" id="userAvatar">
                                <i class="bi bi-person-circle"></i>
                            </div>
                            <div class="user-details" id="userDetails">
                                <div class="user-name">Not signed in</div>
                                <div class="user-email"></div>
                            </div>
                        </div>
                        <button class="drawer-close" id="drawerClose">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                    
                    <div class="drawer-body">
                        <nav class="drawer-nav">
                            <a href="grind.html" class="nav-item">
                                <i class="bi bi-lightning-charge"></i>
                                <span>Grind</span>
                            </a>
                            <a href="study-spaces.html" class="nav-item">
                                <i class="bi bi-geo-alt"></i>
                                <span>Study Spaces</span>
                            </a>
                            <a href="workspace.html" class="nav-item">
                                <i class="bi bi-file-text"></i>
                                <span>Workspace</span>
                            </a>
                            <a href="academic-details.html" class="nav-item">
                                <i class="bi bi-mortarboard"></i>
                                <span>Academic Details</span>
                            </a>
                            <a href="daily-calendar.html" class="nav-item">
                                <i class="bi bi-calendar3"></i>
                                <span>Calendar</span>
                            </a>
                            <a href="extracted.html" class="nav-item">
                                <i class="bi bi-list-task"></i>
                                <span>Tasks</span>
                            </a>
                            <a href="flashcards.html" class="nav-item">
                                <i class="bi bi-card-text"></i>
                                <span>Flashcards</span>
                            </a>
                            <a href="subject-marks.html" class="nav-item">
                                <i class="bi bi-graph-up"></i>
                                <span>Marks</span>
                            </a>
                            <a href="settings.html" class="nav-item">
                                <i class="bi bi-gear"></i>
                                <span>Settings</span>
                            </a>
                        </nav>
                    </div>
                    
                    <div class="drawer-footer">
                        <div class="auth-section" id="authSection">
                            <button class="btn btn-primary" id="signInBtn" style="display: none;">
                                <i class="bi bi-google"></i>
                                Sign in with Google
                            </button>
                            <button class="btn btn-outline-secondary" id="signOutBtn" style="display: none;">
                                <i class="bi bi-box-arrow-right"></i>
                                Sign Out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', drawerHTML);
    }

    setupEventListeners() {
        // Drawer toggle button (usually in header)
        const toggleBtn = document.getElementById('sideDrawerToggle');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggle());
        }

        // Close button
        const closeBtn = document.getElementById('drawerClose');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }

        // Overlay click to close
        const overlay = document.getElementById('drawerOverlay');
        if (overlay) {
            overlay.addEventListener('click', () => this.close());
        }

        // Auth buttons
        const signInBtn = document.getElementById('signInBtn');
        const signOutBtn = document.getElementById('signOutBtn');

        if (signInBtn) {
            signInBtn.addEventListener('click', () => this.signIn());
        }

        if (signOutBtn) {
            signOutBtn.addEventListener('click', () => this.signOut());
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });

        // Cross-tab sync for auth state
        crossTabSync.onUserAction('auth-state-changed', (data) => {
            this.updateUserDisplay(data.user);
        });
    }

    setupAuthStateListener() {
        if (firebaseManager.auth) {
            this.authStateListener = firebaseManager.auth.onAuthStateChanged((user) => {
                this.currentUser = user;
                this.updateUserDisplay(user);
                
                // Broadcast auth state change to other tabs
                crossTabSync.broadcastAction('auth-state-changed', { user: user ? {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName,
                    photoURL: user.photoURL
                } : null });
            });
        }
    }

    updateUserDisplay(user) {
        const userInfo = document.getElementById('userInfo');
        const userAvatar = document.getElementById('userAvatar');
        const userDetails = document.getElementById('userDetails');
        const signInBtn = document.getElementById('signInBtn');
        const signOutBtn = document.getElementById('signOutBtn');

        if (!userInfo || !userDetails || !signInBtn || !signOutBtn) return;

        if (user) {
            // User is signed in
            userDetails.innerHTML = `
                <div class="user-name">${user.displayName || 'User'}</div>
                <div class="user-email">${user.email}</div>
            `;

            if (user.photoURL && userAvatar) {
                userAvatar.innerHTML = `<img src="${user.photoURL}" alt="User Avatar" class="avatar-img">`;
            }

            signInBtn.style.display = 'none';
            signOutBtn.style.display = 'block';
        } else {
            // User is not signed in
            userDetails.innerHTML = `
                <div class="user-name">Not signed in</div>
                <div class="user-email"></div>
            `;

            if (userAvatar) {
                userAvatar.innerHTML = '<i class="bi bi-person-circle"></i>';
            }

            signInBtn.style.display = 'block';
            signOutBtn.style.display = 'none';
        }
    }

    async loadUserData() {
        if (this.currentUser) {
            // Load user-specific data
            try {
                // This could load user preferences, recent activities, etc.
                console.log('Loading user data for:', this.currentUser.email);
            } catch (error) {
                console.error('Failed to load user data:', error);
            }
        }
    }

    async signIn() {
        try {
            await firebaseManager.signInWithGoogle();
            console.log('✅ User signed in successfully');
        } catch (error) {
            console.error('❌ Sign in failed:', error);
            // Show error message to user
            this.showMessage('Sign in failed. Please try again.', 'error');
        }
    }

    async signOut() {
        try {
            await firebaseManager.signOut();
            console.log('✅ User signed out successfully');
        } catch (error) {
            console.error('❌ Sign out failed:', error);
            this.showMessage('Sign out failed. Please try again.', 'error');
        }
    }

    open() {
        const drawer = document.getElementById('sideDrawer');
        if (drawer) {
            drawer.classList.add('open');
            this.isOpen = true;
            document.body.style.overflow = 'hidden';
            
            // Dispatch event
            window.dispatchEvent(new CustomEvent('sidedrawer:opened'));
        }
    }

    close() {
        const drawer = document.getElementById('sideDrawer');
        if (drawer) {
            drawer.classList.remove('open');
            this.isOpen = false;
            document.body.style.overflow = '';
            
            // Dispatch event
            window.dispatchEvent(new CustomEvent('sidedrawer:closed'));
        }
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    showMessage(message, type = 'info') {
        // Create and show a toast message
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    // Cleanup method
    destroy() {
        if (this.authStateListener) {
            this.authStateListener();
        }
        
        const drawer = document.getElementById('sideDrawer');
        if (drawer) {
            drawer.remove();
        }
        
        console.log('🗑️ Side Drawer destroyed');
    }
}

// Create singleton instance
const sideDrawer = new SideDrawer();

// Make available globally for backward compatibility
window.sideDrawer = sideDrawer;

// Export for use in other modules
export default sideDrawer;
export { SideDrawer };
