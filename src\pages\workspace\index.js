// Workspace Page Entry Point
// This is the main entry point for the workspace.html page

// Import core modules
import '../../main.js';

// Import page-specific modules
import { TaskManager } from './task-manager.js';
import { ProjectManager } from './project-manager.js';
import { FlashcardIntegration } from './flashcard-integration.js';
import { WorkspaceAnalyzer } from './workspace-analyzer.js';

// Import page-specific styles
import './workspace.css';

class WorkspacePage {
    constructor() {
        this.initialized = false;
        this.modules = {
            taskManager: null,
            projectManager: null,
            flashcardIntegration: null,
            workspaceAnalyzer: null
        };
        
        this.init();
    }

    async init() {
        if (this.initialized) return;

        console.log('💼 Initializing Workspace Page...');

        try {
            // Wait for core modules to be ready
            await this.waitForCoreModules();
            
            // Initialize page-specific modules
            await this.initializeModules();
            
            // Setup page-specific event listeners
            this.setupEventListeners();
            
            // Load workspace data
            await this.loadWorkspaceData();
            
            this.initialized = true;
            console.log('✅ Workspace Page initialized');
            
            // Dispatch page initialization event
            window.dispatchEvent(new CustomEvent('workspace:page:initialized'));
            
        } catch (error) {
            console.error('❌ Failed to initialize Workspace Page:', error);
        }
    }

    async waitForCoreModules() {
        return new Promise((resolve) => {
            if (window.firebaseManager && window.crossTabSync) {
                resolve();
                return;
            }
            
            window.addEventListener('gpace:initialized', () => {
                resolve();
            }, { once: true });
            
            setTimeout(resolve, 5000);
        });
    }

    async initializeModules() {
        console.log('🔧 Initializing Workspace modules...');

        try {
            // Initialize Task Manager
            this.modules.taskManager = new TaskManager();
            await this.modules.taskManager.init();

            // Initialize Project Manager
            this.modules.projectManager = new ProjectManager();
            await this.modules.projectManager.init();

            // Initialize Flashcard Integration
            this.modules.flashcardIntegration = new FlashcardIntegration();
            await this.modules.flashcardIntegration.init();

            // Initialize Workspace Analyzer
            this.modules.workspaceAnalyzer = new WorkspaceAnalyzer();
            await this.modules.workspaceAnalyzer.init();

            console.log('✅ Workspace modules initialized');

        } catch (error) {
            console.error('❌ Failed to initialize Workspace modules:', error);
            throw error;
        }
    }

    setupEventListeners() {
        // Task management events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="add-task"]')) {
                this.addTask();
            } else if (e.target.matches('[data-action="complete-task"]')) {
                const taskId = e.target.dataset.taskId;
                this.completeTask(taskId);
            } else if (e.target.matches('[data-action="delete-task"]')) {
                const taskId = e.target.dataset.taskId;
                this.deleteTask(taskId);
            }
        });

        // Project management events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="create-project"]')) {
                this.createProject();
            } else if (e.target.matches('[data-action="select-project"]')) {
                const projectId = e.target.dataset.projectId;
                this.selectProject(projectId);
            }
        });

        // Flashcard integration events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="create-flashcard"]')) {
                const taskId = e.target.dataset.taskId;
                this.createFlashcardFromTask(taskId);
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'n':
                        e.preventDefault();
                        this.addTask();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.createProject();
                        break;
                }
            }
        });
    }

    async loadWorkspaceData() {
        try {
            // Load projects
            await this.modules.projectManager.loadProjects();
            
            // Load tasks for current project
            const currentProject = this.modules.projectManager.getCurrentProject();
            if (currentProject) {
                await this.modules.taskManager.loadTasks(currentProject.id);
            }
            
            // Update UI
            this.updateUI();
            
        } catch (error) {
            console.error('Failed to load workspace data:', error);
        }
    }

    updateUI() {
        this.updateProjectsList();
        this.updateTasksList();
        this.updateStats();
    }

    updateProjectsList() {
        const projectsList = document.getElementById('projectsList');
        if (!projectsList) return;

        const projects = this.modules.projectManager.getProjects();
        const currentProject = this.modules.projectManager.getCurrentProject();

        projectsList.innerHTML = projects.map(project => `
            <div class="project-item ${project.id === currentProject?.id ? 'active' : ''}" 
                 data-project-id="${project.id}">
                <div class="project-info">
                    <h4>${project.name}</h4>
                    <p>${project.description || 'No description'}</p>
                    <small>${project.tasks?.length || 0} tasks</small>
                </div>
                <div class="project-actions">
                    <button class="btn btn-sm btn-primary" data-action="select-project" 
                            data-project-id="${project.id}">
                        Select
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateTasksList() {
        const tasksList = document.getElementById('tasksList');
        if (!tasksList) return;

        const tasks = this.modules.taskManager.getTasks();

        tasksList.innerHTML = tasks.map(task => `
            <div class="task-item ${task.completed ? 'completed' : ''}" data-task-id="${task.id}">
                <div class="task-content">
                    <div class="task-checkbox">
                        <input type="checkbox" ${task.completed ? 'checked' : ''} 
                               data-action="complete-task" data-task-id="${task.id}">
                    </div>
                    <div class="task-details">
                        <h5>${task.title}</h5>
                        <p>${task.description || ''}</p>
                        <div class="task-meta">
                            <span class="priority priority-${task.priority}">${task.priority}</span>
                            ${task.dueDate ? `<span class="due-date">${new Date(task.dueDate).toLocaleDateString()}</span>` : ''}
                        </div>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-sm btn-outline-primary" data-action="create-flashcard" 
                            data-task-id="${task.id}" title="Create Flashcard">
                        <i class="bi bi-card-text"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" data-action="delete-task" 
                            data-task-id="${task.id}" title="Delete Task">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateStats() {
        const statsContainer = document.getElementById('workspaceStats');
        if (!statsContainer) return;

        const tasks = this.modules.taskManager.getTasks();
        const completedTasks = tasks.filter(task => task.completed).length;
        const totalTasks = tasks.length;
        const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        statsContainer.innerHTML = `
            <div class="stat-item">
                <div class="stat-value">${totalTasks}</div>
                <div class="stat-label">Total Tasks</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${completedTasks}</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${completionRate}%</div>
                <div class="stat-label">Completion Rate</div>
            </div>
        `;
    }

    async addTask() {
        const taskTitle = prompt('Enter task title:');
        if (!taskTitle) return;

        const taskDescription = prompt('Enter task description (optional):');
        const priority = prompt('Enter priority (low, medium, high):', 'medium');

        const task = {
            title: taskTitle,
            description: taskDescription,
            priority: priority || 'medium',
            completed: false,
            createdAt: new Date(),
            projectId: this.modules.projectManager.getCurrentProject()?.id
        };

        try {
            await this.modules.taskManager.addTask(task);
            this.updateTasksList();
            this.updateStats();
            
            console.log('✅ Task added successfully');
        } catch (error) {
            console.error('Failed to add task:', error);
            alert('Failed to add task. Please try again.');
        }
    }

    async completeTask(taskId) {
        try {
            await this.modules.taskManager.completeTask(taskId);
            this.updateTasksList();
            this.updateStats();
            
            console.log('✅ Task completed');
        } catch (error) {
            console.error('Failed to complete task:', error);
        }
    }

    async deleteTask(taskId) {
        if (!confirm('Are you sure you want to delete this task?')) return;

        try {
            await this.modules.taskManager.deleteTask(taskId);
            this.updateTasksList();
            this.updateStats();
            
            console.log('✅ Task deleted');
        } catch (error) {
            console.error('Failed to delete task:', error);
        }
    }

    async createProject() {
        const projectName = prompt('Enter project name:');
        if (!projectName) return;

        const projectDescription = prompt('Enter project description (optional):');

        const project = {
            name: projectName,
            description: projectDescription,
            createdAt: new Date(),
            tasks: []
        };

        try {
            await this.modules.projectManager.createProject(project);
            this.updateProjectsList();
            
            console.log('✅ Project created successfully');
        } catch (error) {
            console.error('Failed to create project:', error);
            alert('Failed to create project. Please try again.');
        }
    }

    async selectProject(projectId) {
        try {
            await this.modules.projectManager.selectProject(projectId);
            await this.modules.taskManager.loadTasks(projectId);
            
            this.updateUI();
            
            console.log('✅ Project selected');
        } catch (error) {
            console.error('Failed to select project:', error);
        }
    }

    async createFlashcardFromTask(taskId) {
        try {
            const task = this.modules.taskManager.getTask(taskId);
            if (!task) return;

            await this.modules.flashcardIntegration.createFlashcardFromTask(task);
            
            console.log('✅ Flashcard created from task');
            alert('Flashcard created successfully!');
        } catch (error) {
            console.error('Failed to create flashcard from task:', error);
            alert('Failed to create flashcard. Please try again.');
        }
    }

    // Get page status
    getStatus() {
        return {
            initialized: this.initialized,
            modules: Object.keys(this.modules).reduce((status, key) => {
                status[key] = !!this.modules[key];
                return status;
            }, {}),
            currentProject: this.modules.projectManager?.getCurrentProject()?.name || 'None',
            totalTasks: this.modules.taskManager?.getTasks()?.length || 0
        };
    }

    // Cleanup
    destroy() {
        Object.values(this.modules).forEach(module => {
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }
        });
        
        console.log('🗑️ Workspace Page destroyed');
    }
}

// Initialize the page when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.workspacePage = new WorkspacePage();
    });
} else {
    window.workspacePage = new WorkspacePage();
}

// Export for use in other modules
export default WorkspacePage;
