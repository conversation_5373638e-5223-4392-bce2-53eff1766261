// Study Spaces Manager module for Vite bundling
// This replaces the original js/studySpacesManager.js

import { firebaseManager } from '../../core/firebase-init.js';
import crossTabSync from '../../core/cross-tab-sync.js';

class StudySpacesManager {
    constructor() {
        this.initialized = false;
        this.currentLocation = null;
        this.uploadedImages = [];
        this.analysisResults = null;
        this.socketConnection = null;
        
        this.init();
    }

    async init() {
        if (this.initialized) return;

        console.log('🏢 Initializing Study Spaces Manager...');

        try {
            // Wait for Firebase to be ready
            await this.waitForFirebase();
            
            // Setup UI event listeners
            this.setupEventListeners();
            
            // Initialize Socket.IO connection
            await this.initializeSocket();
            
            // Load saved data
            await this.loadSavedData();
            
            this.initialized = true;
            console.log('✅ Study Spaces Manager initialized');
            
            // Dispatch initialization event
            window.dispatchEvent(new CustomEvent('studyspaces:initialized'));
            
        } catch (error) {
            console.error('❌ Failed to initialize Study Spaces Manager:', error);
        }
    }

    async waitForFirebase() {
        return new Promise((resolve) => {
            if (firebaseManager.initialized) {
                resolve();
                return;
            }
            
            window.addEventListener('firebase:initialized', () => {
                resolve();
            }, { once: true });
            
            setTimeout(resolve, 5000);
        });
    }

    setupEventListeners() {
        // File upload handling
        const fileInput = document.getElementById('imageUpload');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }

        // Location selection
        const locationSelect = document.getElementById('locationSelect');
        if (locationSelect) {
            locationSelect.addEventListener('change', (e) => this.handleLocationChange(e));
        }

        // Analysis button
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => this.analyzeStudySpace());
        }

        // Save button
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveStudySpace());
        }

        // Cross-tab sync for study space updates
        crossTabSync.onUserAction('study-space-updated', (data) => {
            this.handleStudySpaceUpdate(data);
        });
    }

    async initializeSocket() {
        try {
            // Initialize Socket.IO connection for real-time features
            if (typeof io !== 'undefined') {
                this.socketConnection = io();
                
                this.socketConnection.on('connect', () => {
                    console.log('🔌 Socket.IO connected');
                });
                
                this.socketConnection.on('timetable-analysis-complete', (data) => {
                    this.handleTimetableAnalysis(data);
                });
                
                this.socketConnection.on('disconnect', () => {
                    console.log('🔌 Socket.IO disconnected');
                });
            }
        } catch (error) {
            console.error('Failed to initialize Socket.IO:', error);
        }
    }

    async handleFileUpload(event) {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        console.log(`📁 Uploading ${files.length} files...`);

        try {
            // Show loading state
            this.showLoadingState('Uploading images...');

            // Process each file
            const uploadPromises = files.map(file => this.processFile(file));
            const results = await Promise.all(uploadPromises);

            // Store uploaded images
            this.uploadedImages = results.filter(result => result.success);

            // Update UI
            this.updateImagePreview();
            this.hideLoadingState();

            console.log(`✅ Successfully uploaded ${this.uploadedImages.length} images`);

        } catch (error) {
            console.error('❌ File upload failed:', error);
            this.showError('Failed to upload images. Please try again.');
            this.hideLoadingState();
        }
    }

    async processFile(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const imageData = {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    data: e.target.result,
                    timestamp: Date.now()
                };
                
                resolve({ success: true, data: imageData });
            };
            
            reader.onerror = () => {
                resolve({ success: false, error: 'Failed to read file' });
            };
            
            reader.readAsDataURL(file);
        });
    }

    updateImagePreview() {
        const previewContainer = document.getElementById('imagePreview');
        if (!previewContainer) return;

        previewContainer.innerHTML = '';

        this.uploadedImages.forEach((image, index) => {
            const imageElement = document.createElement('div');
            imageElement.className = 'image-preview-item';
            imageElement.innerHTML = `
                <img src="${image.data.data}" alt="${image.data.name}" class="preview-image">
                <div class="image-info">
                    <span class="image-name">${image.data.name}</span>
                    <button class="btn btn-sm btn-danger remove-image" data-index="${index}">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;

            // Add remove functionality
            const removeBtn = imageElement.querySelector('.remove-image');
            removeBtn.addEventListener('click', () => this.removeImage(index));

            previewContainer.appendChild(imageElement);
        });
    }

    removeImage(index) {
        this.uploadedImages.splice(index, 1);
        this.updateImagePreview();
    }

    handleLocationChange(event) {
        this.currentLocation = event.target.value;
        console.log(`📍 Location changed to: ${this.currentLocation}`);
        
        // Broadcast location change
        crossTabSync.broadcastAction('location-changed', {
            location: this.currentLocation,
            timestamp: Date.now()
        });
    }

    async analyzeStudySpace() {
        if (this.uploadedImages.length === 0) {
            this.showError('Please upload at least one image to analyze.');
            return;
        }

        console.log('🔍 Starting study space analysis...');

        try {
            this.showLoadingState('Analyzing study space...');

            // Prepare analysis data
            const analysisData = {
                images: this.uploadedImages.map(img => img.data),
                location: this.currentLocation,
                timestamp: Date.now(),
                userId: firebaseManager.getCurrentUser()?.uid
            };

            // Send for analysis (this would typically go to a backend service)
            const results = await this.performAnalysis(analysisData);
            
            this.analysisResults = results;
            this.displayAnalysisResults(results);
            
            this.hideLoadingState();
            console.log('✅ Analysis completed');

        } catch (error) {
            console.error('❌ Analysis failed:', error);
            this.showError('Analysis failed. Please try again.');
            this.hideLoadingState();
        }
    }

    async performAnalysis(data) {
        // This would typically call a backend API or Firebase Function
        // For now, we'll simulate the analysis
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    spaceType: 'Library',
                    noiseLevel: 'Low',
                    lighting: 'Good',
                    crowdedness: 'Moderate',
                    amenities: ['WiFi', 'Power outlets', 'Quiet zones'],
                    suitability: 85,
                    recommendations: [
                        'Great for focused study sessions',
                        'Best during morning hours',
                        'Bring noise-canceling headphones for peak hours'
                    ]
                });
            }, 2000);
        });
    }

    displayAnalysisResults(results) {
        const resultsContainer = document.getElementById('analysisResults');
        if (!resultsContainer) return;

        resultsContainer.innerHTML = `
            <div class="analysis-results">
                <h3>Analysis Results</h3>
                <div class="result-grid">
                    <div class="result-item">
                        <label>Space Type:</label>
                        <span>${results.spaceType}</span>
                    </div>
                    <div class="result-item">
                        <label>Noise Level:</label>
                        <span>${results.noiseLevel}</span>
                    </div>
                    <div class="result-item">
                        <label>Lighting:</label>
                        <span>${results.lighting}</span>
                    </div>
                    <div class="result-item">
                        <label>Crowdedness:</label>
                        <span>${results.crowdedness}</span>
                    </div>
                    <div class="result-item">
                        <label>Suitability Score:</label>
                        <span class="score">${results.suitability}/100</span>
                    </div>
                </div>
                
                <div class="amenities">
                    <h4>Available Amenities:</h4>
                    <ul>
                        ${results.amenities.map(amenity => `<li>${amenity}</li>`).join('')}
                    </ul>
                </div>
                
                <div class="recommendations">
                    <h4>Recommendations:</h4>
                    <ul>
                        ${results.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        resultsContainer.style.display = 'block';
    }

    async saveStudySpace() {
        if (!this.analysisResults) {
            this.showError('Please analyze the study space first.');
            return;
        }

        console.log('💾 Saving study space...');

        try {
            this.showLoadingState('Saving study space...');

            const studySpaceData = {
                location: this.currentLocation,
                images: this.uploadedImages,
                analysis: this.analysisResults,
                timestamp: Date.now(),
                userId: firebaseManager.getCurrentUser()?.uid
            };

            // Save to Firestore
            await this.saveToFirestore(studySpaceData);
            
            // Save to localStorage for offline access
            this.saveToLocalStorage(studySpaceData);
            
            // Broadcast update
            crossTabSync.broadcastAction('study-space-saved', studySpaceData);
            
            this.hideLoadingState();
            this.showSuccess('Study space saved successfully!');
            
            console.log('✅ Study space saved');

        } catch (error) {
            console.error('❌ Failed to save study space:', error);
            this.showError('Failed to save study space. Please try again.');
            this.hideLoadingState();
        }
    }

    async saveToFirestore(data) {
        // Implementation would save to Firestore
        // This is a placeholder for the actual implementation
        console.log('Saving to Firestore:', data);
    }

    saveToLocalStorage(data) {
        const savedSpaces = JSON.parse(localStorage.getItem('studySpaces') || '[]');
        savedSpaces.push(data);
        localStorage.setItem('studySpaces', JSON.stringify(savedSpaces));
    }

    async loadSavedData() {
        try {
            // Load from localStorage
            const savedSpaces = JSON.parse(localStorage.getItem('studySpaces') || '[]');
            console.log(`📚 Loaded ${savedSpaces.length} saved study spaces`);
            
            // Update UI if needed
            this.updateSavedSpacesList(savedSpaces);
            
        } catch (error) {
            console.error('Failed to load saved data:', error);
        }
    }

    updateSavedSpacesList(spaces) {
        const listContainer = document.getElementById('savedSpacesList');
        if (!listContainer) return;

        listContainer.innerHTML = spaces.map(space => `
            <div class="saved-space-item">
                <h4>${space.location}</h4>
                <p>Suitability: ${space.analysis?.suitability || 'N/A'}/100</p>
                <small>${new Date(space.timestamp).toLocaleDateString()}</small>
            </div>
        `).join('');
    }

    handleStudySpaceUpdate(data) {
        console.log('📡 Received study space update:', data);
        // Handle cross-tab updates
        this.loadSavedData();
    }

    handleTimetableAnalysis(data) {
        console.log('📅 Received timetable analysis:', data);
        // Handle timetable analysis results from Socket.IO
    }

    showLoadingState(message) {
        const loadingElement = document.getElementById('loadingState');
        if (loadingElement) {
            loadingElement.textContent = message;
            loadingElement.style.display = 'block';
        }
    }

    hideLoadingState() {
        const loadingElement = document.getElementById('loadingState');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }

    showError(message) {
        console.error(message);
        // Implementation would show error toast/modal
    }

    showSuccess(message) {
        console.log(message);
        // Implementation would show success toast/modal
    }

    // Cleanup method
    destroy() {
        if (this.socketConnection) {
            this.socketConnection.disconnect();
        }
        console.log('🗑️ Study Spaces Manager destroyed');
    }
}

// Create singleton instance
const studySpacesManager = new StudySpacesManager();

// Make available globally for backward compatibility
window.studySpacesManager = studySpacesManager;
window.StudySpacesManager = StudySpacesManager;

// Export for use in other modules
export default studySpacesManager;
export { StudySpacesManager };
