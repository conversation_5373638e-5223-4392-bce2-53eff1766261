// Header injection module for Vite bundling
// This replaces the original js/inject-header.js

class HeaderInjector {
    constructor() {
        this.initialized = false;
        this.init();
    }

    init() {
        if (this.initialized) return;

        console.log('🎯 Initializing Header Injector...');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.injectHeader());
        } else {
            this.injectHeader();
        }

        this.initialized = true;
    }

    injectHeader() {
        try {
            // Create audio element for alarm sound
            this.createAlarmAudio();
            
            // Add import map for Firebase (if not using bundled version)
            this.addImportMap();
            
            // Add meta tags and other header elements
            this.addMetaTags();
            
            console.log('✅ Header injection completed');
            
            // Dispatch event
            window.dispatchEvent(new CustomEvent('header:injected'));
            
        } catch (error) {
            console.error('❌ Header injection failed:', error);
        }
    }

    createAlarmAudio() {
        // Check if audio element already exists
        if (document.getElementById('alarm-sound')) {
            return;
        }

        const alarmAudio = document.createElement('audio');
        alarmAudio.id = 'alarm-sound';
        alarmAudio.src = '/alarm-sounds/alexa-ringtone.mp3';
        alarmAudio.preload = 'auto';
        alarmAudio.style.display = 'none';
        
        document.body.appendChild(alarmAudio);
        console.log('🔊 Alarm audio element created');
    }

    addImportMap() {
        // Check if import map already exists
        if (document.querySelector('script[type="importmap"]')) {
            return;
        }

        // Only add import map if not using bundled Firebase
        if (!window.__VITE_BUNDLED__) {
            const moduleScript = document.createElement('script');
            moduleScript.type = 'importmap';
            moduleScript.textContent = JSON.stringify({
                imports: {
                    'firebase/app': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js',
                    'firebase/firestore': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js',
                    'firebase/auth': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js',
                    'firebase/functions': 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js'
                }
            });
            
            document.head.appendChild(moduleScript);
            console.log('🗺️ Import map added');
        }
    }

    addMetaTags() {
        // Add viewport meta tag if not present
        if (!document.querySelector('meta[name="viewport"]')) {
            const viewportMeta = document.createElement('meta');
            viewportMeta.name = 'viewport';
            viewportMeta.content = 'width=device-width, initial-scale=1.0';
            document.head.appendChild(viewportMeta);
        }

        // Add charset meta tag if not present
        if (!document.querySelector('meta[charset]')) {
            const charsetMeta = document.createElement('meta');
            charsetMeta.setAttribute('charset', 'UTF-8');
            document.head.insertBefore(charsetMeta, document.head.firstChild);
        }

        // Add theme color meta tag
        if (!document.querySelector('meta[name="theme-color"]')) {
            const themeColorMeta = document.createElement('meta');
            themeColorMeta.name = 'theme-color';
            themeColorMeta.content = '#1a1a1a';
            document.head.appendChild(themeColorMeta);
        }

        // Add preconnect links for external resources
        this.addPreconnectLinks();
    }

    addPreconnectLinks() {
        const preconnectDomains = [
            'https://www.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://cdnjs.cloudflare.com',
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com'
        ];

        preconnectDomains.forEach(domain => {
            // Check if preconnect already exists
            if (!document.querySelector(`link[href="${domain}"]`)) {
                const preconnectLink = document.createElement('link');
                preconnectLink.rel = 'preconnect';
                preconnectLink.href = domain;
                preconnectLink.crossOrigin = 'anonymous';
                document.head.appendChild(preconnectLink);
            }
        });

        console.log('🔗 Preconnect links added');
    }

    // Method to inject custom CSS
    injectCSS(css) {
        const style = document.createElement('style');
        style.textContent = css;
        document.head.appendChild(style);
    }

    // Method to inject custom JavaScript
    injectJS(js) {
        const script = document.createElement('script');
        script.textContent = js;
        document.body.appendChild(script);
    }

    // Method to add external script
    addExternalScript(src, options = {}) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            
            // Apply options
            if (options.async) script.async = true;
            if (options.defer) script.defer = true;
            if (options.type) script.type = options.type;
            if (options.crossOrigin) script.crossOrigin = options.crossOrigin;
            
            script.onload = resolve;
            script.onerror = reject;
            
            document.head.appendChild(script);
        });
    }

    // Method to add external stylesheet
    addExternalStylesheet(href, options = {}) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            
            // Apply options
            if (options.crossOrigin) link.crossOrigin = options.crossOrigin;
            if (options.integrity) link.integrity = options.integrity;
            
            link.onload = resolve;
            link.onerror = reject;
            
            document.head.appendChild(link);
        });
    }

    // Method to preload resources
    preloadResource(href, as, options = {}) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = href;
        link.as = as;
        
        // Apply options
        if (options.crossOrigin) link.crossOrigin = options.crossOrigin;
        if (options.type) link.type = options.type;
        
        document.head.appendChild(link);
        console.log(`⚡ Preloading resource: ${href}`);
    }
}

// Create singleton instance
const headerInjector = new HeaderInjector();

// Make available globally for backward compatibility
window.headerInjector = headerInjector;

// Export for use in other modules
export default headerInjector;
export { HeaderInjector };
