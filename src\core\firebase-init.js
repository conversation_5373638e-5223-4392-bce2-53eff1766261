// Firebase initialization module for Vite bundling
// This replaces the original js/firebaseAuth.js and js/firebaseConfig.js
// Optimized to only import used Firebase modules

import { initializeApp } from 'firebase/app';
import {
    getAuth,
    GoogleAuthProvider,
    signInWithPopup,
    signOut,
    onAuthStateChanged
} from 'firebase/auth';
import {
    getFirestore,
    connectFirestoreEmulator
} from 'firebase/firestore';
import {
    getFunctions,
    connectFunctionsEmulator
} from 'firebase/functions';

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
    authDomain: "mzm-gpace.firebaseapp.com",
    projectId: "mzm-gpace",
    storageBucket: "mzm-gpace.firebasestorage.app",
    messagingSenderId: "949014366726",
    appId: "1:949014366726:web:3aa05a6e133e2066c45187"
};

class FirebaseManager {
    constructor() {
        this.app = null;
        this.auth = null;
        this.db = null;
        this.functions = null;
        this.provider = null;
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) {
            return this;
        }

        try {
            console.log('🔥 Initializing Firebase...');
            
            // Initialize Firebase app
            this.app = initializeApp(firebaseConfig);
            
            // Initialize services
            this.auth = getAuth(this.app);
            this.db = getFirestore(this.app);
            this.functions = getFunctions(this.app);
            
            // Setup Google Auth provider
            this.provider = new GoogleAuthProvider();
            this.provider.setCustomParameters({ prompt: 'select_account' });
            
            // Make available globally for backward compatibility
            window.auth = this.auth;
            window.db = this.db;
            window.functions = this.functions;
            
            // Setup global auth functions
            window.signInWithGoogle = () => this.signInWithGoogle();
            window.signOutUser = () => this.signOut();
            
            this.initialized = true;
            console.log('✅ Firebase initialized successfully');
            
            // Dispatch initialization event
            window.dispatchEvent(new CustomEvent('firebase:initialized', {
                detail: { 
                    auth: this.auth, 
                    db: this.db, 
                    functions: this.functions 
                }
            }));
            
            return this;
            
        } catch (error) {
            console.error('❌ Firebase initialization failed:', error);
            throw error;
        }
    }

    async signInWithGoogle() {
        try {
            console.log('🔐 Signing in with Google...');
            const result = await signInWithPopup(this.auth, this.provider);
            console.log('✅ Google sign-in successful:', result.user.email);
            return result;
        } catch (error) {
            console.error('❌ Google sign-in failed:', error);
            throw error;
        }
    }

    async signOut() {
        try {
            console.log('🚪 Signing out...');
            await signOut(this.auth);
            console.log('✅ Sign-out successful');
        } catch (error) {
            console.error('❌ Sign-out failed:', error);
            throw error;
        }
    }

    // Get current user
    getCurrentUser() {
        return this.auth?.currentUser || null;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return !!this.getCurrentUser();
    }

    // Listen for auth state changes
    onAuthStateChanged(callback) {
        if (this.auth) {
            return this.auth.onAuthStateChanged(callback);
        }
        return () => {}; // Return empty unsubscribe function
    }
}

// Create singleton instance
const firebaseManager = new FirebaseManager();

// Initialize immediately
firebaseManager.initialize().catch(error => {
    console.error('Failed to initialize Firebase:', error);
});

// Export for use in other modules
export default firebaseManager;
export { firebaseManager, firebaseConfig };
