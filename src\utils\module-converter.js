// Module Converter Utility
// Helps convert global scripts to ES modules and manage dependencies

class ModuleConverter {
    constructor() {
        this.convertedModules = new Map();
        this.dependencyGraph = new Map();
        this.globalVariables = new Set();
        this.conversionQueue = [];
    }

    // Register a global script for conversion
    registerScript(scriptPath, dependencies = [], exports = []) {
        const moduleInfo = {
            path: scriptPath,
            dependencies,
            exports,
            converted: false,
            loaded: false
        };

        this.dependencyGraph.set(scriptPath, moduleInfo);
        this.conversionQueue.push(scriptPath);
    }

    // Convert a global script to ES module format
    async convertScript(scriptPath) {
        if (this.convertedModules.has(scriptPath)) {
            return this.convertedModules.get(scriptPath);
        }

        console.log(`🔄 Converting script to module: ${scriptPath}`);

        try {
            // Load the original script content
            const scriptContent = await this.loadScriptContent(scriptPath);
            
            // Analyze dependencies and exports
            const analysis = this.analyzeScript(scriptContent);
            
            // Generate ES module wrapper
            const moduleContent = this.generateModuleWrapper(scriptContent, analysis);
            
            // Create module blob URL
            const moduleBlob = new Blob([moduleContent], { type: 'application/javascript' });
            const moduleUrl = URL.createObjectURL(moduleBlob);
            
            const convertedModule = {
                originalPath: scriptPath,
                moduleUrl,
                analysis,
                content: moduleContent
            };

            this.convertedModules.set(scriptPath, convertedModule);
            console.log(`✅ Script converted: ${scriptPath}`);
            
            return convertedModule;

        } catch (error) {
            console.error(`❌ Failed to convert script ${scriptPath}:`, error);
            throw error;
        }
    }

    // Load script content from URL
    async loadScriptContent(scriptPath) {
        try {
            const response = await fetch(scriptPath);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.text();
        } catch (error) {
            console.error(`Failed to load script content from ${scriptPath}:`, error);
            throw error;
        }
    }

    // Analyze script for dependencies and exports
    analyzeScript(content) {
        const analysis = {
            dependencies: [],
            exports: [],
            globalVariables: [],
            windowAssignments: [],
            requiresCrossTabs: false,
            requiresFirebase: false,
            requiresDOM: false
        };

        // Check for common patterns
        if (content.includes('window.crossTabSync') || content.includes('crossTabSync')) {
            analysis.requiresCrossTabs = true;
            analysis.dependencies.push('../core/cross-tab-sync.js');
        }

        if (content.includes('window.auth') || content.includes('firebase') || content.includes('getFirestore')) {
            analysis.requiresFirebase = true;
            analysis.dependencies.push('../core/firebase-init.js');
        }

        if (content.includes('document.') || content.includes('window.addEventListener')) {
            analysis.requiresDOM = true;
        }

        // Find window assignments (potential exports)
        const windowAssignmentRegex = /window\.(\w+)\s*=/g;
        let match;
        while ((match = windowAssignmentRegex.exec(content)) !== null) {
            analysis.windowAssignments.push(match[1]);
            analysis.exports.push(match[1]);
        }

        // Find global variable declarations
        const globalVarRegex = /(var|let|const)\s+(\w+)/g;
        while ((match = globalVarRegex.exec(content)) !== null) {
            analysis.globalVariables.push(match[2]);
        }

        // Find function declarations
        const functionRegex = /function\s+(\w+)/g;
        while ((match = functionRegex.exec(content)) !== null) {
            analysis.exports.push(match[1]);
        }

        // Find class declarations
        const classRegex = /class\s+(\w+)/g;
        while ((match = classRegex.exec(content)) !== null) {
            analysis.exports.push(match[1]);
        }

        return analysis;
    }

    // Generate ES module wrapper for the script
    generateModuleWrapper(originalContent, analysis) {
        let moduleContent = '';

        // Add imports
        if (analysis.dependencies.length > 0) {
            analysis.dependencies.forEach(dep => {
                if (dep.includes('cross-tab-sync')) {
                    moduleContent += `import crossTabSync from '${dep}';\n`;
                } else if (dep.includes('firebase-init')) {
                    moduleContent += `import { firebaseManager } from '${dep}';\n`;
                }
            });
            moduleContent += '\n';
        }

        // Add DOM ready wrapper if needed
        if (analysis.requiresDOM) {
            moduleContent += `// DOM ready wrapper\n`;
            moduleContent += `function initializeWhenReady(initFn) {\n`;
            moduleContent += `    if (document.readyState === 'loading') {\n`;
            moduleContent += `        document.addEventListener('DOMContentLoaded', initFn);\n`;
            moduleContent += `    } else {\n`;
            moduleContent += `        initFn();\n`;
            moduleContent += `    }\n`;
            moduleContent += `}\n\n`;
        }

        // Wrap original content in an IIFE to avoid global pollution
        moduleContent += `// Original script content wrapped in module\n`;
        moduleContent += `(function() {\n`;
        
        // Replace window assignments with local variables
        let processedContent = originalContent;
        analysis.windowAssignments.forEach(varName => {
            processedContent = processedContent.replace(
                new RegExp(`window\\.${varName}\\s*=`, 'g'),
                `const ${varName} =`
            );
        });

        moduleContent += processedContent;
        moduleContent += `\n})();\n\n`;

        // Add exports
        if (analysis.exports.length > 0) {
            moduleContent += `// Exports\n`;
            analysis.exports.forEach(exportName => {
                moduleContent += `export { ${exportName} };\n`;
                // Also make available globally for backward compatibility
                moduleContent += `window.${exportName} = ${exportName};\n`;
            });
        }

        // Add default export if there's a main class or function
        if (analysis.exports.length > 0) {
            const mainExport = analysis.exports[0];
            moduleContent += `\nexport default ${mainExport};\n`;
        }

        return moduleContent;
    }

    // Load converted module
    async loadConvertedModule(scriptPath) {
        const convertedModule = await this.convertScript(scriptPath);
        
        try {
            const module = await import(convertedModule.moduleUrl);
            console.log(`✅ Module loaded: ${scriptPath}`);
            return module;
        } catch (error) {
            console.error(`❌ Failed to load converted module ${scriptPath}:`, error);
            throw error;
        }
    }

    // Convert and load multiple scripts with dependency resolution
    async convertAndLoadScripts(scriptPaths) {
        console.log(`🔄 Converting and loading ${scriptPaths.length} scripts...`);

        const results = [];
        
        for (const scriptPath of scriptPaths) {
            try {
                const module = await this.loadConvertedModule(scriptPath);
                results.push({ path: scriptPath, module, success: true });
            } catch (error) {
                console.error(`Failed to convert/load ${scriptPath}:`, error);
                results.push({ path: scriptPath, error, success: false });
            }
        }

        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        console.log(`✅ Conversion complete: ${successful} successful, ${failed} failed`);
        
        return results;
    }

    // Batch convert common script patterns
    async convertCommonScripts() {
        const commonScripts = [
            {
                path: '/js/sideDrawer.js',
                dependencies: ['../core/cross-tab-sync.js', '../core/firebase-init.js'],
                exports: ['sideDrawer', 'SideDrawer']
            },
            {
                path: '/js/common.js',
                dependencies: ['../core/firebase-init.js'],
                exports: ['commonUtils', 'ensureDataInitialized']
            },
            {
                path: '/js/storageManager.js',
                dependencies: [],
                exports: ['storageManager', 'StorageManager']
            },
            {
                path: '/js/theme-manager.js',
                dependencies: ['../core/cross-tab-sync.js'],
                exports: ['themeManager', 'ThemeManager']
            }
        ];

        // Register scripts
        commonScripts.forEach(script => {
            this.registerScript(script.path, script.dependencies, script.exports);
        });

        // Convert and load
        const scriptPaths = commonScripts.map(s => s.path);
        return await this.convertAndLoadScripts(scriptPaths);
    }

    // Get conversion statistics
    getStats() {
        return {
            totalScripts: this.dependencyGraph.size,
            convertedScripts: this.convertedModules.size,
            queuedScripts: this.conversionQueue.length,
            globalVariables: this.globalVariables.size
        };
    }

    // Cleanup converted modules
    cleanup() {
        this.convertedModules.forEach(module => {
            if (module.moduleUrl) {
                URL.revokeObjectURL(module.moduleUrl);
            }
        });
        
        this.convertedModules.clear();
        this.dependencyGraph.clear();
        this.globalVariables.clear();
        this.conversionQueue = [];
        
        console.log('🗑️ Module converter cleaned up');
    }
}

// Create singleton instance
const moduleConverter = new ModuleConverter();

// Make available globally
window.moduleConverter = moduleConverter;

// Export for use in other modules
export default moduleConverter;
export { ModuleConverter };
