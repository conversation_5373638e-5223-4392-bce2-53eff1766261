// Code Splitting Manager
// Implements intelligent code splitting for optimal loading performance

class CodeSplittingManager {
    constructor() {
        this.splitPoints = new Map();
        this.loadedChunks = new Set();
        this.preloadQueue = [];
        this.criticalChunks = new Set();
        this.initialized = false;
        
        this.init();
    }

    init() {
        if (this.initialized) return;

        console.log('🔀 Initializing Code Splitting Manager...');

        // Define split points
        this.defineSplitPoints();
        
        // Setup preloading strategies
        this.setupPreloadingStrategies();
        
        // Setup intersection observers for lazy loading
        this.setupLazyLoading();
        
        this.initialized = true;
        console.log('✅ Code Splitting Manager initialized');
    }

    defineSplitPoints() {
        // Critical chunks - loaded immediately
        this.criticalChunks.add('core-modules');
        this.criticalChunks.add('vendor-firebase');
        this.criticalChunks.add('vendor-ui');

        // Page-specific split points
        this.splitPoints.set('study-spaces', {
            chunks: ['page-study-spaces-new', 'vendor-charts'],
            preload: ['features-new'],
            lazy: ['vendor-3d', 'vendor-math'],
            priority: 'high',
            route: '/study-spaces.html'
        });

        this.splitPoints.set('workspace', {
            chunks: ['page-workspace-new', 'vendor-editor'],
            preload: ['features-new'],
            lazy: ['vendor-charts', 'vendor-pdf'],
            priority: 'high',
            route: '/workspace.html'
        });

        this.splitPoints.set('grind', {
            chunks: ['grind', 'vendor-math'],
            preload: ['vendor-3d'],
            lazy: ['vendor-pdf', 'vendor-charts'],
            priority: 'medium',
            route: '/grind.html'
        });

        this.splitPoints.set('flashcards', {
            chunks: ['page-flashcards-new'],
            preload: ['vendor-charts'],
            lazy: ['vendor-editor', 'vendor-3d'],
            priority: 'medium',
            route: '/flashcards.html'
        });

        this.splitPoints.set('academic-details', {
            chunks: ['page-academic-new', 'vendor-charts'],
            preload: ['vendor-pdf'],
            lazy: ['vendor-editor', 'vendor-3d'],
            priority: 'medium',
            route: '/academic-details.html'
        });

        // Feature-specific split points
        this.splitPoints.set('charts', {
            chunks: ['vendor-charts', 'features-new'],
            trigger: '[data-chart]',
            priority: 'high',
            type: 'feature'
        });

        this.splitPoints.set('pdf-viewer', {
            chunks: ['vendor-pdf'],
            trigger: '[data-pdf]',
            priority: 'medium',
            type: 'feature'
        });

        this.splitPoints.set('3d-visualization', {
            chunks: ['vendor-3d'],
            trigger: '[data-3d]',
            priority: 'low',
            type: 'feature'
        });

        this.splitPoints.set('rich-editor', {
            chunks: ['vendor-editor'],
            trigger: '[data-editor]',
            priority: 'high',
            type: 'feature'
        });
    }

    setupPreloadingStrategies() {
        // Preload based on user behavior patterns
        this.setupHoverPreloading();
        this.setupViewportPreloading();
        this.setupPredictivePreloading();
    }

    setupHoverPreloading() {
        // Preload chunks when user hovers over navigation links
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('a[href]');
            if (link) {
                const href = link.getAttribute('href');
                const page = this.getPageFromHref(href);
                
                if (page && this.splitPoints.has(page)) {
                    this.preloadPageChunks(page);
                }
            }
        });
    }

    setupViewportPreloading() {
        // Preload chunks for elements entering viewport
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const feature = element.dataset.feature;
                        
                        if (feature && this.splitPoints.has(feature)) {
                            this.preloadFeatureChunks(feature);
                        }
                    }
                });
            }, {
                rootMargin: '100px',
                threshold: 0.1
            });

            // Observe elements with data-feature attributes
            document.querySelectorAll('[data-feature]').forEach(el => {
                observer.observe(el);
            });
        }
    }

    setupPredictivePreloading() {
        // Preload likely next pages based on current page
        const currentPage = this.getCurrentPage();
        const predictions = this.getPredictedPages(currentPage);
        
        // Preload predicted pages with low priority
        predictions.forEach(page => {
            setTimeout(() => {
                this.preloadPageChunks(page, 'low');
            }, 2000); // Delay to avoid blocking critical resources
        });
    }

    setupLazyLoading() {
        // Setup lazy loading for non-critical features
        if ('IntersectionObserver' in window) {
            const lazyObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const feature = element.dataset.lazyFeature;
                        
                        if (feature && this.splitPoints.has(feature)) {
                            this.loadFeatureChunks(feature);
                            lazyObserver.unobserve(element);
                        }
                    }
                });
            }, {
                rootMargin: '50px',
                threshold: 0.1
            });

            // Observe elements with data-lazy-feature attributes
            document.querySelectorAll('[data-lazy-feature]').forEach(el => {
                lazyObserver.observe(el);
            });
        }
    }

    async preloadPageChunks(page, priority = 'medium') {
        const splitPoint = this.splitPoints.get(page);
        if (!splitPoint) return;

        console.log(`⚡ Preloading chunks for page: ${page}`);

        try {
            // Preload main chunks
            const preloadPromises = splitPoint.chunks.map(chunk => 
                this.preloadChunk(chunk, priority)
            );

            // Preload additional chunks if specified
            if (splitPoint.preload) {
                splitPoint.preload.forEach(chunk => {
                    preloadPromises.push(this.preloadChunk(chunk, 'low'));
                });
            }

            await Promise.allSettled(preloadPromises);
            console.log(`✅ Preloaded chunks for page: ${page}`);

        } catch (error) {
            console.error(`Failed to preload chunks for page ${page}:`, error);
        }
    }

    async preloadFeatureChunks(feature) {
        const splitPoint = this.splitPoints.get(feature);
        if (!splitPoint || splitPoint.type !== 'feature') return;

        console.log(`⚡ Preloading chunks for feature: ${feature}`);

        try {
            const preloadPromises = splitPoint.chunks.map(chunk => 
                this.preloadChunk(chunk, splitPoint.priority)
            );

            await Promise.allSettled(preloadPromises);
            console.log(`✅ Preloaded chunks for feature: ${feature}`);

        } catch (error) {
            console.error(`Failed to preload chunks for feature ${feature}:`, error);
        }
    }

    async loadFeatureChunks(feature) {
        const splitPoint = this.splitPoints.get(feature);
        if (!splitPoint || splitPoint.type !== 'feature') return;

        console.log(`🔄 Loading chunks for feature: ${feature}`);

        try {
            const loadPromises = splitPoint.chunks.map(chunk => 
                this.loadChunk(chunk)
            );

            await Promise.all(loadPromises);
            console.log(`✅ Loaded chunks for feature: ${feature}`);

            // Dispatch feature loaded event
            window.dispatchEvent(new CustomEvent('feature-chunks-loaded', {
                detail: { feature, chunks: splitPoint.chunks }
            }));

        } catch (error) {
            console.error(`Failed to load chunks for feature ${feature}:`, error);
        }
    }

    async preloadChunk(chunkName, priority = 'medium') {
        if (this.loadedChunks.has(chunkName)) {
            return; // Already loaded
        }

        try {
            // Create preload link
            const link = document.createElement('link');
            link.rel = 'modulepreload';
            link.href = this.getChunkUrl(chunkName);
            
            // Set priority based on importance
            if (priority === 'high') {
                link.fetchPriority = 'high';
            } else if (priority === 'low') {
                link.fetchPriority = 'low';
            }

            document.head.appendChild(link);

            console.log(`⚡ Preloading chunk: ${chunkName} (${priority} priority)`);

        } catch (error) {
            console.error(`Failed to preload chunk ${chunkName}:`, error);
        }
    }

    async loadChunk(chunkName) {
        if (this.loadedChunks.has(chunkName)) {
            return; // Already loaded
        }

        try {
            // Dynamic import of the chunk
            const chunkUrl = this.getChunkUrl(chunkName);
            await import(chunkUrl);
            
            this.loadedChunks.add(chunkName);
            console.log(`✅ Loaded chunk: ${chunkName}`);

        } catch (error) {
            console.error(`Failed to load chunk ${chunkName}:`, error);
            throw error;
        }
    }

    getChunkUrl(chunkName) {
        // In production, this would be the actual chunk URL
        // For development, we'll use a placeholder
        const isDev = import.meta.env.DEV;
        const baseUrl = isDev ? '/src/' : '/dist/js/';
        
        return `${baseUrl}${chunkName}.js`;
    }

    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().replace('.html', '') || 'index';
        return filename;
    }

    getPageFromHref(href) {
        if (!href || href.startsWith('#') || href.startsWith('http')) {
            return null;
        }
        
        const filename = href.split('/').pop().replace('.html', '');
        return filename;
    }

    getPredictedPages(currentPage) {
        // Predict likely next pages based on current page
        const predictions = {
            'study-spaces': ['workspace', 'academic-details'],
            'workspace': ['flashcards', 'study-spaces'],
            'grind': ['workspace', 'flashcards'],
            'flashcards': ['workspace', 'academic-details'],
            'academic-details': ['study-spaces', 'subject-marks'],
            'daily-calendar': ['workspace', 'study-spaces']
        };

        return predictions[currentPage] || [];
    }

    // Performance monitoring
    measureChunkLoadTime(chunkName, startTime) {
        const loadTime = performance.now() - startTime;
        console.log(`📊 Chunk ${chunkName} loaded in ${loadTime.toFixed(2)}ms`);
        
        // Report to analytics if available
        if (window.gtag) {
            window.gtag('event', 'chunk_load_time', {
                chunk_name: chunkName,
                load_time: Math.round(loadTime)
            });
        }
    }

    // Get splitting statistics
    getStats() {
        return {
            totalSplitPoints: this.splitPoints.size,
            loadedChunks: this.loadedChunks.size,
            criticalChunks: this.criticalChunks.size,
            preloadQueue: this.preloadQueue.length,
            splitPointsByType: {
                page: Array.from(this.splitPoints.values()).filter(sp => sp.type !== 'feature').length,
                feature: Array.from(this.splitPoints.values()).filter(sp => sp.type === 'feature').length
            }
        };
    }

    // Cleanup
    destroy() {
        this.splitPoints.clear();
        this.loadedChunks.clear();
        this.preloadQueue = [];
        this.criticalChunks.clear();
        this.initialized = false;
        
        console.log('🗑️ Code Splitting Manager destroyed');
    }
}

// Create singleton instance
const codeSplittingManager = new CodeSplittingManager();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        codeSplittingManager.init();
    });
} else {
    codeSplittingManager.init();
}

// Make available globally
window.codeSplittingManager = codeSplittingManager;

// Export for use in other modules
export default codeSplittingManager;
export { CodeSplittingManager };
