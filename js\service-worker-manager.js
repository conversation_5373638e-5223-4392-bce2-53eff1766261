// Centralized Service Worker Manager
// Eliminates duplicate registrations and provides unified SW management

class ServiceWorkerManager {
    constructor() {
        this.registrations = new Map();
        this.isSupported = 'serviceWorker' in navigator;
        this.initialized = false;
        
        if (!this.isSupported) {
            console.warn('⚠️ Service Workers not supported in this browser');
            return;
        }
        
        this.init();
    }

    async init() {
        if (this.initialized) return;
        
        console.log('🔧 Initializing Service Worker Manager');
        
        // Register the alarm service worker
        await this.registerAlarmServiceWorker();
        
        this.initialized = true;
        console.log('✅ Service Worker Manager initialized');
    }

    async registerAlarmServiceWorker() {
        const swPath = '/js/alarm-service-worker.js';
        const registrationKey = 'alarm-service-worker';
        
        try {
            // Check if already registered
            if (this.registrations.has(registrationKey)) {
                console.log('🔄 Alarm Service Worker already registered');
                return this.registrations.get(registrationKey);
            }

            const registration = await navigator.serviceWorker.register(swPath);
            this.registrations.set(registrationKey, registration);
            
            console.log('✅ Alarm Service Worker registered successfully');
            
            // Set up message handling
            this.setupMessageHandling(registration);
            
            return registration;
        } catch (error) {
            console.error('❌ Alarm Service Worker registration failed:', error);
            throw error;
        }
    }

    setupMessageHandling(registration) {
        // Listen for messages from service worker
        if (navigator.serviceWorker.controller) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                this.handleServiceWorkerMessage(event);
            });
        }

        // Handle service worker updates
        registration.addEventListener('updatefound', () => {
            console.log('🔄 Service Worker update found');
        });
    }

    handleServiceWorkerMessage(event) {
        const { type, data } = event.data || {};
        
        switch (type) {
            case 'ALARM_TRIGGERED':
                this.handleAlarmTriggered(data);
                break;
            case 'TIMER_NOTIFICATION':
                this.handleTimerNotification(data);
                break;
            default:
                console.log('📨 Service Worker message:', event.data);
        }
    }

    handleAlarmTriggered(data) {
        console.log('⏰ Alarm triggered:', data);
        // Broadcast to cross-tab sync if available
        if (window.crossTabSync) {
            window.crossTabSync.send('alarm-triggered', data);
        }
    }

    handleTimerNotification(data) {
        console.log('⏱️ Timer notification:', data);
        // Broadcast to cross-tab sync if available
        if (window.crossTabSync) {
            window.crossTabSync.send('timer-notification', data);
        }
    }

    // Send message to service worker
    async postMessage(type, data) {
        if (!this.isSupported) {
            console.warn('⚠️ Service Workers not supported - cannot send message');
            return;
        }

        try {
            const registration = this.registrations.get('alarm-service-worker');
            if (registration && registration.active) {
                registration.active.postMessage({ type, data });
                console.log(`📤 Message sent to Service Worker: ${type}`, data);
            } else {
                console.warn('⚠️ Service Worker not active - cannot send message');
            }
        } catch (error) {
            console.error('❌ Failed to send message to Service Worker:', error);
        }
    }

    // Sync alarms with service worker
    async syncAlarms(alarms) {
        await this.postMessage('SYNC_ALARMS', { alarms });
    }

    // Sync pomodoro timer with service worker
    async syncPomodoroTimer(timerData) {
        await this.postMessage('POMODORO_TIMER', timerData);
    }

    // Get registration status
    getRegistrationStatus(key = 'alarm-service-worker') {
        const registration = this.registrations.get(key);
        return {
            registered: !!registration,
            active: !!(registration && registration.active),
            installing: !!(registration && registration.installing),
            waiting: !!(registration && registration.waiting)
        };
    }

    // Cleanup method
    async unregisterAll() {
        if (!this.isSupported) return;

        for (const [key, registration] of this.registrations) {
            try {
                await registration.unregister();
                console.log(`🗑️ Unregistered Service Worker: ${key}`);
            } catch (error) {
                console.error(`❌ Failed to unregister Service Worker ${key}:`, error);
            }
        }
        
        this.registrations.clear();
        this.initialized = false;
    }
}

// Create global instance
window.serviceWorkerManager = new ServiceWorkerManager();

export default window.serviceWorkerManager;