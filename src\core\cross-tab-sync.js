// Cross-Tab Synchronization Module for Vite bundling
// This replaces the original js/cross-tab-sync.js

class CrossTabSync {
    constructor(namespace = 'gpace') {
        this.namespace = namespace;
        this.channel = new BroadcastChannel(this.namespace);
        this.listeners = new Map();
        this.initialized = false;

        this.init();
    }

    init() {
        console.log(`🔗 CrossTabSync initialized for namespace: ${this.namespace}`);

        // Listen for messages from other tabs
        this.channel.onmessage = (event) => {
            const { type, data } = event.data;
            console.log(`📡 Received cross-tab message: ${type}`, data);
            this.handleMessage(type, data);
        };

        // Setup storage listener for localStorage changes
        this.setupStorageListener();
        
        // Initialize debugging
        this.initDebug();
        
        this.initialized = true;
        
        // Dispatch initialization event
        window.dispatchEvent(new CustomEvent('crosstab:initialized', {
            detail: { namespace: this.namespace }
        }));
    }

    // Send message to other tabs
    send(type, data) {
        const message = { type, data, timestamp: Date.now() };
        this.channel.postMessage(message);
        console.log(`📤 Sent cross-tab message: ${type}`, data);
    }

    // Handle incoming messages
    handleMessage(type, data) {
        const listeners = this.listeners.get(type);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in cross-tab listener for ${type}:`, error);
                }
            });
        }
    }

    // Add event listener
    on(type, callback) {
        if (!this.listeners.has(type)) {
            this.listeners.set(type, new Set());
        }
        this.listeners.get(type).add(callback);
        
        console.log(`📝 Added cross-tab listener for: ${type}`);
    }

    // Remove event listener
    off(type, callback) {
        const listeners = this.listeners.get(type);
        if (listeners) {
            listeners.delete(callback);
            if (listeners.size === 0) {
                this.listeners.delete(type);
            }
        }
    }

    // Broadcast user actions across tabs
    broadcastAction(actionType, actionData) {
        this.send('user-action', { type: actionType, data: actionData });
    }

    // Listen for specific user actions
    onUserAction(actionType, callback) {
        this.on('user-action', (action) => {
            if (action.type === actionType) {
                callback(action.data);
            }
        });
    }

    // Synchronize localStorage across tabs
    syncStorage(key, value) {
        if (value !== undefined) {
            localStorage.setItem(key, JSON.stringify(value));
            this.send('storage-sync', { key, value });
        }
        return JSON.parse(localStorage.getItem(key) || 'null');
    }

    // Setup storage listener for localStorage changes
    setupStorageListener() {
        window.addEventListener('storage', (event) => {
            if (event.key && event.newValue !== event.oldValue) {
                console.log(`💾 Storage changed: ${event.key}`);
                this.send('storage-change', {
                    key: event.key,
                    oldValue: event.oldValue,
                    newValue: event.newValue
                });
            }
        });

        // Listen for storage sync messages
        this.on('storage-sync', (data) => {
            const { key, value } = data;
            localStorage.setItem(key, JSON.stringify(value));
        });

        // Listen for storage change messages
        this.on('storage-change', (data) => {
            console.log(`📦 Cross-tab storage change: ${data.key}`);
            // Trigger custom event for storage changes
            window.dispatchEvent(new CustomEvent('crosstab:storage-change', {
                detail: data
            }));
        });
    }

    // Synchronize application state
    syncState(stateKey, newState) {
        if (newState !== undefined) {
            localStorage.setItem(stateKey, JSON.stringify(newState));
            this.send('state-sync', { key: stateKey, state: newState });
        }
        return JSON.parse(localStorage.getItem(stateKey) || 'null');
    }

    // Listen for state synchronization
    onStateSync(stateKey, callback) {
        this.on('state-sync', (data) => {
            if (data.key === stateKey) {
                callback(data.state);
            }
        });
    }

    // Test communication between tabs
    testCommunication() {
        const testData = {
            message: 'Cross-tab communication test',
            timestamp: Date.now(),
            tabId: Math.random().toString(36).substr(2, 9)
        };
        
        this.send('cross-tab-test', testData);
        console.log('🧪 Cross-tab test message sent:', testData);
    }

    // Check browser support
    checkBrowserSupport() {
        const features = {
            broadcastChannel: 'BroadcastChannel' in window,
            localStorage: this.isLocalStorageAvailable(),
            customEvents: 'CustomEvent' in window
        };

        console.log('🔍 Browser support check:', features);
        
        if (!features.broadcastChannel) {
            console.warn('⚠️ BroadcastChannel not supported - cross-tab sync will be limited');
        }
        
        return features;
    }

    // Check if localStorage is available
    isLocalStorageAvailable() {
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            return true;
        } catch(e) {
            return false;
        }
    }

    // Initialize debugging
    initDebug() {
        // Test communication on initialization
        this.testCommunication();

        // Check browser support
        this.checkBrowserSupport();

        // Listen for test messages
        this.on('cross-tab-test', (testData) => {
            console.log('✅ Cross-Tab Test Received:', testData);
        });
    }

    // Cleanup method
    destroy() {
        if (this.channel) {
            this.channel.close();
        }
        this.listeners.clear();
        console.log('🗑️ CrossTabSync destroyed');
    }
}

// Create singleton instance
const crossTabSync = new CrossTabSync();

// Make available globally for backward compatibility
window.crossTabSync = crossTabSync;

// Export for use in other modules
export default crossTabSync;
export { CrossTabSync };
