/**
 * Centralized Module Loader
 * Manages loading of external libraries and internal modules
 * Replaces scattered script tags with a unified loading system
 */

import { initializeModuleSystem, dynamicImport } from './import-maps.js';

/**
 * Module loading configurations for different pages
 */
const moduleConfigs = {
  // Common modules loaded on all pages
  common: {
    css: [
      'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
      'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css'
    ],
    modules: [
      '@core/sync',
      '@core/common',
      '@ui/theme',
      '@services/alarm'
    ],
    scripts: [
      'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js'
    ]
  },

  // Landing page specific modules
  landing: {
    css: [
      './styles/main.css',
      './css/sideDrawer.css'
    ],
    modules: [],
    scripts: []
  },

  // Grind page specific modules
  grind: {
    css: [
      './grind.css',
      './css/task-display.css',
      './css/text-expansion.css',
      './css/simulation-enhancer.css',
      './css/ai-search-response.css',
      './css/task-notes.css',
      './css/taskLinks.css'
    ],
    modules: [
      '@core/firebase',
      '@core/firestore',
      './js/energyHologram.js',
      './js/ai-researcher.js',
      './js/text-expansion.js',
      './js/simulation-enhancer.js',
      './js/flashcardTaskIntegration.js'
    ],
    scripts: [
      'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js',
      'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js'
    ],
    external: {
      'three': 'https://unpkg.com/three@0.157.0/build/three.module.js',
      '@google/generative-ai': 'https://esm.run/@google/generative-ai'
    }
  },

  // Workspace page specific modules
  workspace: {
    css: [
      './css/workspace.css',
      'https://cdn.quilljs.com/1.3.6/quill.snow.css'
    ],
    modules: [
      './js/workspace-core.js',
      './js/workspace-ui.js',
      './js/workspace-formatting.js',
      './js/workspace-document.js',
      './js/workspace-media.js',
      './js/workspace-tables-links.js',
      './js/workspace-attachments.js',
      './js/workspaceFlashcardIntegration.js'
    ],
    scripts: [
      'https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js',
      'https://cdn.quilljs.com/1.3.6/quill.min.js',
      'https://apis.google.com/js/api.js',
      'https://accounts.google.com/gsi/client',
      'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js'
    ]
  },

  // Study spaces page specific modules
  'study-spaces': {
    css: [
      './styles/study-spaces.css'
    ],
    modules: [
      './js/timetableAnalyzer.js',
      './js/studySpacesManager.js',
      './js/studySpaceAnalyzer.js',
      './js/scheduleManager.js',
      './js/imageAnalyzer.js'
    ],
    scripts: []
  },

  // Academic details page specific modules
  'academic-details': {
    css: [
      './css/academic-details.css'
    ],
    modules: [
      './js/academic-details.js',
      './js/subject-management.js',
      './js/semester-management.js'
    ],
    scripts: []
  },

  // Flashcards page specific modules
  flashcards: {
    css: [
      './css/flashcards.css'
    ],
    modules: [
      './js/flashcards.js',
      './js/flashcardManager.js'
    ],
    scripts: []
  },

  // Tasks page specific modules
  tasks: {
    css: [
      './styles/tasks.css'
    ],
    modules: [
      './js/tasksManager.js',
      './js/taskFilters.js',
      './js/taskAttachments.js'
    ],
    scripts: []
  }
};

/**
 * Module Loader Class
 */
class ModuleLoader {
  constructor() {
    this.loadedModules = new Set();
    this.loadedCSS = new Set();
    this.loadedScripts = new Set();
    this.currentPage = this.detectCurrentPage();
  }

  /**
   * Detect current page from URL
   */
  detectCurrentPage() {
    const path = window.location.pathname;
    const filename = path.split('/').pop().replace('.html', '') || 'index';
    return filename === 'index' ? 'landing' : filename;
  }

  /**
   * Load CSS files
   */
  async loadCSS(cssFiles) {
    const promises = cssFiles.map(href => {
      if (this.loadedCSS.has(href)) return Promise.resolve();
      
      return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.onload = () => {
          this.loadedCSS.add(href);
          resolve();
        };
        link.onerror = reject;
        document.head.appendChild(link);
      });
    });

    await Promise.all(promises);
  }

  /**
   * Load JavaScript modules
   */
  async loadModules(moduleList) {
    const promises = moduleList.map(async (moduleSpecifier) => {
      if (this.loadedModules.has(moduleSpecifier)) return;
      
      try {
        await dynamicImport(moduleSpecifier);
        this.loadedModules.add(moduleSpecifier);
        console.log(`✅ Loaded module: ${moduleSpecifier}`);
      } catch (error) {
        console.error(`❌ Failed to load module: ${moduleSpecifier}`, error);
      }
    });

    await Promise.all(promises);
  }

  /**
   * Load external scripts
   */
  async loadScripts(scriptList) {
    const promises = scriptList.map(src => {
      if (this.loadedScripts.has(src)) return Promise.resolve();
      
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.defer = true;
        script.onload = () => {
          this.loadedScripts.add(src);
          resolve();
        };
        script.onerror = reject;
        document.head.appendChild(script);
      });
    });

    await Promise.all(promises);
  }

  /**
   * Load all resources for a specific page
   */
  async loadPageResources(pageName = this.currentPage) {
    console.log(`🚀 Loading resources for page: ${pageName}`);
    
    // Always load common resources first
    const commonConfig = moduleConfigs.common;
    const pageConfig = moduleConfigs[pageName] || {};

    try {
      // Load CSS files
      const allCSS = [...(commonConfig.css || []), ...(pageConfig.css || [])];
      if (allCSS.length > 0) {
        await this.loadCSS(allCSS);
        console.log('✅ CSS files loaded');
      }

      // Load modules
      const allModules = [...(commonConfig.modules || []), ...(pageConfig.modules || [])];
      if (allModules.length > 0) {
        await this.loadModules(allModules);
        console.log('✅ Modules loaded');
      }

      // Load external scripts
      const allScripts = [...(commonConfig.scripts || []), ...(pageConfig.scripts || [])];
      if (allScripts.length > 0) {
        await this.loadScripts(allScripts);
        console.log('✅ Scripts loaded');
      }

      console.log(`🎉 All resources loaded for ${pageName}`);
    } catch (error) {
      console.error(`❌ Error loading resources for ${pageName}:`, error);
    }
  }

  /**
   * Initialize the module loader
   */
  async initialize() {
    // Initialize import maps first
    initializeModuleSystem();
    
    // Wait a bit for import maps to be processed
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Load page-specific resources
    await this.loadPageResources();
  }
}

// Create global instance
const moduleLoader = new ModuleLoader();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    moduleLoader.initialize();
  });
} else {
  moduleLoader.initialize();
}

// Export for manual usage
export default moduleLoader;
export { ModuleLoader };
